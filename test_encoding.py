#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للترميز العربي
"""

import csv
import os

def test_arabic_encoding():
    """اختبار قراءة وكتابة النصوص العربية"""
    
    # نص عربي للاختبار
    test_data = [
        ['facebook_id', 'fullname', 'email', 'phone', 'gender', 'hometown'],
        ['100001', 'أحمد محمد علي', '<EMAIL>', '01012345678', 'ذكر', 'القاهرة، مصر'],
        ['100002', 'فاطمة علي حسن', '<EMAIL>', '01098765432', 'أنثى', 'الإسكندرية، مصر'],
        ['100003', 'محمد أحمد سالم', '<EMAIL>', '01155443322', 'ذكر', 'الجيزة، مصر']
    ]
    
    print("🧪 اختبار الترميز العربي...")
    print("=" * 50)
    
    # اختبار الكتابة بترميزات مختلفة
    encodings = [
        ('utf-8', 'UTF-8 عادي'),
        ('utf-8-sig', 'UTF-8 مع BOM'),
        ('cp1256', 'Windows Arabic'),
        ('iso-8859-6', 'Arabic ISO')
    ]
    
    for encoding, description in encodings:
        try:
            filename = f'test_output_{encoding.replace("-", "_")}.csv'
            
            # كتابة الملف
            with open(filename, 'w', newline='', encoding=encoding) as f:
                writer = csv.writer(f, delimiter=',', quotechar='"', quoting=csv.QUOTE_MINIMAL)
                writer.writerows(test_data)
            
            # قراءة الملف للتحقق
            with open(filename, 'r', encoding=encoding) as f:
                reader = csv.reader(f)
                rows = list(reader)
            
            print(f"✅ {description} ({encoding}): نجح")
            print(f"   الملف: {filename}")
            print(f"   عينة: {rows[1][1]}")  # عرض الاسم العربي
            print()
            
        except Exception as e:
            print(f"❌ {description} ({encoding}): فشل")
            print(f"   الخطأ: {str(e)}")
            print()
    
    print("🔍 اختبار قراءة الملفات...")
    print("=" * 50)
    
    # اختبار قراءة ملف موجود
    test_files = ['test_arabic_data.csv', 'sample_messy_data.csv']
    
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"📄 اختبار ملف: {test_file}")
            
            for encoding, description in encodings:
                try:
                    with open(test_file, 'r', encoding=encoding) as f:
                        sample = f.read(200)
                        # التحقق من وجود نصوص عربية صحيحة
                        arabic_chars = sum(1 for char in sample if '\u0600' <= char <= '\u06FF')
                        
                    print(f"   {description}: {arabic_chars} حرف عربي")
                    
                except Exception as e:
                    print(f"   {description}: خطأ - {str(e)}")
            
            print()
    
    print("✅ انتهى الاختبار!")
    print("\nالتوصيات:")
    print("- استخدم UTF-8 مع BOM للتوافق مع Excel")
    print("- استخدم UTF-8 عادي للتوافق مع البرامج الأخرى")
    print("- تأكد من حفظ نسختين بترميزات مختلفة")

if __name__ == "__main__":
    test_arabic_encoding()

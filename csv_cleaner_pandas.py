#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧹 CSV Cleaner Tool - Pandas Version
أداة تنظيف CSV باستخدام Pandas - أسرع وأكثر دقة
"""

import os
import pandas as pd
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from threading import Thread
import re
from pathlib import Path

class PandasCSVCleaner:
    def __init__(self, root):
        """تهيئة التطبيق"""
        self.root = root
        self.root.title("🧹 CSV Cleaner Tool - Pandas Version")
        self.root.geometry("800x600")
        self.root.configure(pady=10, padx=10)
        
        # النسق الثابت (19 عمود)
        self.standard_headers = [
            'facebook_id', 'email', 'phone', 'religion', 'birthday_year',
            'first_name', 'last_name', 'gender', 'link', 'username',
            'fullname', 'beo', 'company', 'title', 'hometown', 
            'country', 'education', 'user', 'status'
        ]
        
        # المتغيرات
        self.input_folder = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.progress = tk.DoubleVar()
        self.status = tk.StringVar(value="جاهز للبدء...")
        self.current_file = tk.StringVar()
        self.processing = False
        
        # خيارات التنظيف
        self.remove_duplicates = tk.BooleanVar(value=True)
        self.standardize_headers = tk.BooleanVar(value=True)
        self.clean_data = tk.BooleanVar(value=True)
        self.remove_empty_rows = tk.BooleanVar(value=True)
        
        # خيارات تصدير الملف المستخرج
        self.output_format = tk.StringVar(value="csv")  # csv, txt, both
        
        # إعداد الواجهة
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_label = tk.Label(
            self.root,
            text="🧹 CSV Cleaner Tool - Pandas Version",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50'
        )
        title_label.pack(pady=(0, 20))
        
        # قسم المجلدات
        self.create_folder_section()
        
        # قسم الخيارات
        self.create_options_section()
        
        # قسم التحكم
        self.create_control_section()
        
        # قسم النتائج
        self.create_results_section()

    def create_folder_section(self):
        """إنشاء قسم اختيار المجلدات"""
        folder_frame = ttk.LabelFrame(self.root, text="📁 اختيار المجلدات", padding=10)
        folder_frame.pack(fill='x', pady=(0, 10))
        
        # مجلد الإدخال
        ttk.Label(folder_frame, text="📂 مجلد ملفات CSV:").pack(anchor='w')
        input_frame = ttk.Frame(folder_frame)
        input_frame.pack(fill='x', pady=5)
        ttk.Entry(input_frame, textvariable=self.input_folder).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(input_frame, text="تصفح", command=self.browse_input).pack(side='right')
        
        # مجلد الإخراج
        ttk.Label(folder_frame, text="💾 مجلد النتائج:").pack(anchor='w', pady=(10, 0))
        output_frame = ttk.Frame(folder_frame)
        output_frame.pack(fill='x', pady=5)
        ttk.Entry(output_frame, textvariable=self.output_folder).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(output_frame, text="تصفح", command=self.browse_output).pack(side='right')

    def create_options_section(self):
        """إنشاء قسم الخيارات"""
        options_frame = ttk.LabelFrame(self.root, text="⚙️ خيارات التنظيف المتقدمة", padding=10)
        options_frame.pack(fill='x', pady=(0, 10))
        
        # العمود الأيسر
        left_column = ttk.Frame(options_frame)
        left_column.pack(side='left', fill='both', expand=True)
        
        ttk.Checkbutton(left_column, text="🔧 توحيد رؤوس الأعمدة (19 عمود)", 
                       variable=self.standardize_headers).pack(anchor='w', pady=2)
        ttk.Checkbutton(left_column, text="🧹 تنظيف البيانات والفواصل", 
                       variable=self.clean_data).pack(anchor='w', pady=2)
        ttk.Checkbutton(left_column, text="🗑️ إزالة المكررات", 
                       variable=self.remove_duplicates).pack(anchor='w', pady=2)
        ttk.Checkbutton(left_column, text="📄 حذف الصفوف الفارغة", 
                       variable=self.remove_empty_rows).pack(anchor='w', pady=2)
        
        # العمود الأيمن
        right_column = ttk.Frame(options_frame)
        right_column.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        # قسم اختيار تنسيق الإخراج
        output_format_frame = ttk.LabelFrame(right_column, text="💾 تنسيق الإخراج", padding=5)
        output_format_frame.pack(fill='x')
        
        ttk.Radiobutton(output_format_frame, text="📈 CSV فقط", 
                       variable=self.output_format, value="csv").pack(anchor='w')
        ttk.Radiobutton(output_format_frame, text="📝 TXT فقط", 
                       variable=self.output_format, value="txt").pack(anchor='w')
        ttk.Radiobutton(output_format_frame, text="📊 CSV و TXT معاً", 
                       variable=self.output_format, value="both").pack(anchor='w')

    def create_control_section(self):
        """إنشاء قسم التحكم"""
        control_frame = ttk.LabelFrame(self.root, text="🎮 التحكم والتقدم", padding=10)
        control_frame.pack(fill='x', pady=(0, 10))
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack(fill='x', pady=(0, 10))
        
        self.start_button = ttk.Button(buttons_frame, text="🚀 بدء التنظيف المتقدم", 
                                      command=self.start_cleaning)
        self.start_button.pack(side='left', padx=(0, 10))
        
        self.stop_button = ttk.Button(buttons_frame, text="⏹️ إيقاف", 
                                     command=self.stop_cleaning, state='disabled')
        self.stop_button.pack(side='left', padx=(0, 10))
        
        ttk.Button(buttons_frame, text="📁 فتح النتائج", 
                  command=self.open_results).pack(side='left')
        
        # شريط التقدم
        ttk.Label(control_frame, text="📊 التقدم:").pack(anchor='w')
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress, 
                                           maximum=100, mode='determinate')
        self.progress_bar.pack(fill='x', pady=5)
        
        # معلومات الحالة
        ttk.Label(control_frame, text="📄 الملف الحالي:").pack(anchor='w', pady=(10, 0))
        ttk.Label(control_frame, textvariable=self.current_file, 
                 font=('Arial', 9), foreground='blue').pack(anchor='w')
        
        ttk.Label(control_frame, text="ℹ️ الحالة:").pack(anchor='w', pady=(5, 0))
        ttk.Label(control_frame, textvariable=self.status, 
                 font=('Arial', 9), foreground='navy').pack(anchor='w')

    def create_results_section(self):
        """إنشاء قسم النتائج"""
        results_frame = ttk.LabelFrame(self.root, text="📊 نتائج المعالجة", padding=10)
        results_frame.pack(fill='both', expand=True)
        
        # منطقة النص للنتائج
        self.results_text = tk.Text(results_frame, height=8, wrap=tk.WORD, 
                                   font=('Consolas', 9))
        scrollbar = ttk.Scrollbar(results_frame, orient="vertical", command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        self.results_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def log_result(self, message):
        """إضافة رسالة للنتائج"""
        self.results_text.insert(tk.END, f"{message}\n")
        self.results_text.see(tk.END)
        self.root.update_idletasks()

    def browse_input(self):
        """تصفح مجلد الإدخال"""
        folder = filedialog.askdirectory(title="اختر مجلد ملفات CSV")
        if folder:
            self.input_folder.set(folder)
            csv_files = list(Path(folder).glob("*.csv"))
            self.status.set(f"تم اختيار المجلد - وجد {len(csv_files)} ملف CSV")
            self.log_result(f"📁 تم اختيار المجلد: {folder}")
            self.log_result(f"📈 ملفات CSV: {len(csv_files)}")

    def browse_output(self):
        """تصفح مجلد الإخراج"""
        folder = filedialog.askdirectory(title="اختر مجلد النتائج")
        if folder:
            self.output_folder.set(folder)
            self.log_result(f"💾 تم اختيار مجلد النتائج: {folder}")

    def start_cleaning(self):
        """بدء عملية التنظيف"""
        if not self.input_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإدخال")
            return
            
        if not self.output_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإخراج")
            return
        
        # مسح منطقة النتائج
        self.results_text.delete(1.0, tk.END)
        
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.processing = True
        
        Thread(target=self.cleaning_thread, daemon=True).start()

    def stop_cleaning(self):
        """إيقاف عملية التنظيف"""
        self.processing = False
        self.status.set("جاري إيقاف العملية...")
        self.log_result("⏹️ تم طلب إيقاف العملية")

    def cleaning_thread(self):
        """معالجة الملفات في thread منفصل"""
        try:
            self.log_result("🚀 بدء عملية التنظيف المتقدمة باستخدام Pandas...")
            
            input_path = Path(self.input_folder.get())
            csv_files = list(input_path.glob("*.csv"))
            
            self.log_result(f"📊 عدد الملفات للمعالجة: {len(csv_files)}")
            
            if not csv_files:
                self.log_result("❌ لا توجد ملفات CSV في المجلد")
                messagebox.showwarning("تنبيه", "لا توجد ملفات CSV في المجلد")
                return
            
            total_files = len(csv_files)
            processed_files = 0
            
            for i, csv_file in enumerate(csv_files):
                if not self.processing:
                    break
                
                self.log_result(f"\n📄 معالجة الملف {i+1}/{total_files}: {csv_file.name}")
                
                # تحديث الواجهة
                self.current_file.set(csv_file.name)
                self.status.set(f"معالجة الملف {i+1} من {total_files}")
                self.progress.set((i / total_files) * 100)
                self.root.update_idletasks()
                
                # معالجة الملف باستخدام Pandas
                if self.process_file_pandas(csv_file):
                    processed_files += 1
                    self.log_result(f"✅ تم معالجة {csv_file.name} بنجاح")
                else:
                    self.log_result(f"❌ فشل في معالجة {csv_file.name}")
                
                # تحديث التقدم
                self.progress.set(((i + 1) / total_files) * 100)
                self.root.update_idletasks()
            
            if self.processing:
                success_msg = f"✅ تم الانتهاء! معالج {processed_files} من {total_files} ملف"
                self.status.set(success_msg)
                self.log_result(f"\n🎉 {success_msg}")
                messagebox.showinfo("مكتمل", f"تم تنظيف {processed_files} ملف بنجاح!")
            else:
                self.status.set("❌ تم إيقاف العملية")
                self.log_result("❌ تم إيقاف العملية بواسطة المستخدم")
                
        except Exception as e:
            error_msg = f"حدث خطأ: {str(e)}"
            self.log_result(f"💥 خطأ عام: {error_msg}")
            messagebox.showerror("خطأ", error_msg)
        finally:
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')

    def process_file_pandas(self, input_file):
        """معالجة ملف باستخدام Pandas - أسرع وأكثر دقة"""
        try:
            self.log_result(f"📋 قراءة الملف باستخدام Pandas...")
            
            # 1️⃣ قراءة الملف مع التعامل مع القيم بين دبل كوتشين
            df = pd.read_csv(input_file, quotechar='"', skipinitialspace=True, 
                           encoding='utf-8', on_bad_lines='skip')
            
            self.log_result(f"📊 تم قراءة {len(df)} سطر و {len(df.columns)} عمود")
            
            # 2️⃣ حذف الصفوف الفارغة بالكامل
            if self.remove_empty_rows.get():
                original_rows = len(df)
                df.dropna(how='all', inplace=True)
                removed_rows = original_rows - len(df)
                if removed_rows > 0:
                    self.log_result(f"🗑️ تم حذف {removed_rows} صف فارغ")
            
            # 3️⃣ تنظيف البيانات
            if self.clean_data.get():
                self.log_result(f"🧹 تنظيف البيانات...")
                
                # تنظيف المسافات الزائدة من النصوص
                df = df.applymap(lambda x: x.strip() if isinstance(x, str) else x)
                
                # حذف الفواصل داخل النصوص
                for col in df.columns:
                    if df[col].dtype == 'object':  # أعمدة نصية
                        df[col] = df[col].astype(str).str.replace(',', '', regex=False)
                        df[col] = df[col].str.replace('"', '', regex=False)
                        df[col] = df[col].str.replace("'", '', regex=False)
                
                self.log_result(f"✅ تم تنظيف البيانات")
            
            # 4️⃣ توحيد الرؤوس
            if self.standardize_headers.get():
                self.log_result(f"🔧 توحيد رؤوس الأعمدة...")
                
                # إنشاء DataFrame جديد بالنسق الثابت
                new_df = pd.DataFrame(columns=self.standard_headers)
                
                # نسخ البيانات المتاحة
                for i, col in enumerate(self.standard_headers):
                    if i < len(df.columns):
                        new_df[col] = df.iloc[:, i].values
                
                df = new_df
                self.log_result(f"🔧 تم توحيد الرؤوس: {len(df.columns)} عمود")
            
            # 5️⃣ إزالة المكررات
            if self.remove_duplicates.get():
                original_rows = len(df)
                df.drop_duplicates(inplace=True)
                removed_duplicates = original_rows - len(df)
                if removed_duplicates > 0:
                    self.log_result(f"🗑️ تم حذف {removed_duplicates} مكرر")
            
            # 6️⃣ حفظ الملف
            self.log_result(f"💾 حفظ الملف...")
            saved_files = self.save_pandas_output(df, input_file.stem)
            
            if saved_files:
                files_info = ", ".join([f.name for f in saved_files])
                self.log_result(f"💾 تم حفظ: {files_info}")
                self.log_result(f"📊 البيانات النهائية: {len(df)} سجل, {len(df.columns)} عمود")
                return True
            else:
                self.log_result(f"❌ فشل في حفظ الملفات")
                return False
                
        except Exception as e:
            self.log_result(f"💥 خطأ في معالجة {input_file.name}: {str(e)}")
            return False

    def save_pandas_output(self, df, base_filename):
        """حفظ الملفات باستخدام Pandas"""
        saved_files = []
        output_format = self.output_format.get()
        
        try:
            if output_format == "csv" or output_format == "both":
                csv_file = Path(self.output_folder.get()) / f"{base_filename}_cleaned.csv"
                df.to_csv(csv_file, index=False, encoding='utf-8')
                saved_files.append(csv_file)
            
            if output_format == "txt" or output_format == "both":
                txt_file = Path(self.output_folder.get()) / f"{base_filename}_cleaned.txt"
                df.to_csv(txt_file, index=False, sep='\t', encoding='utf-8')
                saved_files.append(txt_file)
            
            return saved_files
            
        except Exception as e:
            self.log_result(f"💥 خطأ في حفظ الملفات: {str(e)}")
            return []

    def open_results(self):
        """فتح مجلد النتائج"""
        if self.output_folder.get() and os.path.exists(self.output_folder.get()):
            os.startfile(self.output_folder.get())


def main():
    """تشغيل التطبيق"""
    root = tk.Tk()
    app = PandasCSVCleaner(root)
    root.mainloop()


if __name__ == "__main__":
    main()

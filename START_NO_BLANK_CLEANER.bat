@echo off
title CSV Cleaner - No Blank Columns

echo ========================================
echo    CSV Cleaner - No Blank Columns
echo ========================================
echo.
echo Starting CSV Cleaner without blank columns...
echo This version:
echo - Removes blank columns from headers AND data
echo - Produces 19 columns exactly
echo - Perfect alignment for Text to Columns
echo.

python csv_cleaner_no_blank.py

if errorlevel 1 (
    echo.
    echo Failed to start CSV Cleaner
    echo Please make sure Python is installed
    pause
)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 الأداة النهائية الشاملة لتنظيف البيانات
FINAL ULTIMATE DATA CLEANER - All-in-One Solution
"""

import os
import re
import csv
import sqlite3
import pandas as pd
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from threading import Thread
from pathlib import Path

class UltimateDataCleaner:
    def __init__(self, root):
        """تهيئة الأداة النهائية"""
        self.root = root
        self.root.title("🎯 الأداة النهائية الشاملة لتنظيف البيانات")
        self.root.geometry("900x700")
        self.root.configure(pady=10, padx=10)
        
        # الفاصل الذكي المميز
        self.SMART_DELIMITER = "⟨⟩"  # فاصل مميز يصعب وجوده في البيانات
        
        # النسق الثابت النهائي
        self.FINAL_HEADERS = [
            'facebook_id', 'email', 'phone', 'religion', 'birthday_year',
            'first_name', 'last_name', 'gender', 'link', 'username',
            'fullname', 'beo', 'company', 'title', 'hometown', 
            'country', 'education', 'user', 'status'
        ]
        
        # أنماط البيانات للاستخراج الذكي
        self.DATA_PATTERNS = {
            'facebook_id': r'\d{10,20}',
            'phone': r'\+?\d{10,15}',
            'email': r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
            'url': r'https?://[^\s⟨⟩]+',
            'date': r'\d{1,2}/\d{1,2}/\d{4}',
            'location': r'[A-Z][a-z]+\s+[A-Z][a-z]+'
        }
        
        # المتغيرات
        self.input_folder = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.progress = tk.DoubleVar()
        self.status = tk.StringVar(value="جاهز للبدء...")
        self.processing = False
        
        # خيارات المعالجة
        self.fix_merged_data = tk.BooleanVar(value=True)
        self.use_smart_delimiter = tk.BooleanVar(value=True)
        self.create_database = tk.BooleanVar(value=True)
        self.output_format = tk.StringVar(value="all")  # csv, db, all
        
        self.setup_ui()

    def setup_ui(self):
        """إعداد الواجهة النهائية"""
        # العنوان
        title_label = tk.Label(
            self.root,
            text="🎯 الأداة النهائية الشاملة لتنظيف البيانات",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50'
        )
        title_label.pack(pady=(0, 20))
        
        # معلومات الفاصل الذكي
        info_frame = ttk.LabelFrame(self.root, text="ℹ️ معلومات الفاصل الذكي", padding=10)
        info_frame.pack(fill='x', pady=(0, 10))
        
        info_text = f"الفاصل الذكي المستخدم: {self.SMART_DELIMITER}\nهذا الفاصل مميز ولا يوجد في البيانات العادية"
        ttk.Label(info_frame, text=info_text, font=('Arial', 9)).pack(anchor='w')
        
        # قسم المجلدات
        self.create_folder_section()
        
        # قسم الخيارات
        self.create_options_section()
        
        # قسم التحكم
        self.create_control_section()
        
        # قسم النتائج
        self.create_results_section()

    def create_folder_section(self):
        """قسم اختيار المجلدات"""
        folder_frame = ttk.LabelFrame(self.root, text="📁 اختيار المجلدات", padding=10)
        folder_frame.pack(fill='x', pady=(0, 10))
        
        # مجلد الإدخال
        ttk.Label(folder_frame, text="📂 مجلد البيانات الخام:").pack(anchor='w')
        input_frame = ttk.Frame(folder_frame)
        input_frame.pack(fill='x', pady=5)
        ttk.Entry(input_frame, textvariable=self.input_folder).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(input_frame, text="تصفح", command=self.browse_input).pack(side='right')
        
        # مجلد الإخراج
        ttk.Label(folder_frame, text="💾 مجلد النتائج النهائية:").pack(anchor='w', pady=(10, 0))
        output_frame = ttk.Frame(folder_frame)
        output_frame.pack(fill='x', pady=5)
        ttk.Entry(output_frame, textvariable=self.output_folder).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(output_frame, text="تصفح", command=self.browse_output).pack(side='right')

    def create_options_section(self):
        """قسم الخيارات النهائية"""
        options_frame = ttk.LabelFrame(self.root, text="⚙️ خيارات المعالجة النهائية", padding=10)
        options_frame.pack(fill='x', pady=(0, 10))
        
        # العمود الأيسر
        left_column = ttk.Frame(options_frame)
        left_column.pack(side='left', fill='both', expand=True)
        
        ttk.Checkbutton(left_column, text="🔧 إصلاح البيانات الملتصقة", 
                       variable=self.fix_merged_data).pack(anchor='w', pady=2)
        ttk.Checkbutton(left_column, text="⟨⟩ استخدام الفاصل الذكي", 
                       variable=self.use_smart_delimiter).pack(anchor='w', pady=2)
        ttk.Checkbutton(left_column, text="🗄️ إنشاء قاعدة بيانات SQLite", 
                       variable=self.create_database).pack(anchor='w', pady=2)
        
        # العمود الأيمن
        right_column = ttk.Frame(options_frame)
        right_column.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        # خيارات الإخراج
        output_frame = ttk.LabelFrame(right_column, text="💾 تنسيق الإخراج", padding=5)
        output_frame.pack(fill='x')
        
        ttk.Radiobutton(output_frame, text="📈 CSV فقط", 
                       variable=self.output_format, value="csv").pack(anchor='w')
        ttk.Radiobutton(output_frame, text="🗄️ قاعدة بيانات فقط", 
                       variable=self.output_format, value="db").pack(anchor='w')
        ttk.Radiobutton(output_frame, text="📊 الكل (CSV + DB)", 
                       variable=self.output_format, value="all").pack(anchor='w')

    def create_control_section(self):
        """قسم التحكم"""
        control_frame = ttk.LabelFrame(self.root, text="🎮 التحكم والتقدم", padding=10)
        control_frame.pack(fill='x', pady=(0, 10))
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack(fill='x', pady=(0, 10))
        
        self.start_button = ttk.Button(buttons_frame, text="🚀 بدء المعالجة النهائية", 
                                      command=self.start_processing)
        self.start_button.pack(side='left', padx=(0, 10))
        
        self.stop_button = ttk.Button(buttons_frame, text="⏹️ إيقاف", 
                                     command=self.stop_processing, state='disabled')
        self.stop_button.pack(side='left', padx=(0, 10))
        
        ttk.Button(buttons_frame, text="📁 فتح النتائج", 
                  command=self.open_results).pack(side='left')
        
        # شريط التقدم
        ttk.Label(control_frame, text="📊 التقدم:").pack(anchor='w')
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress, 
                                           maximum=100, mode='determinate')
        self.progress_bar.pack(fill='x', pady=5)
        
        # الحالة
        ttk.Label(control_frame, text="ℹ️ الحالة:").pack(anchor='w', pady=(5, 0))
        ttk.Label(control_frame, textvariable=self.status, 
                 font=('Arial', 9), foreground='navy').pack(anchor='w')

    def create_results_section(self):
        """قسم النتائج"""
        results_frame = ttk.LabelFrame(self.root, text="📊 نتائج المعالجة", padding=10)
        results_frame.pack(fill='both', expand=True)
        
        self.results_text = tk.Text(results_frame, wrap=tk.WORD, font=('Consolas', 9))
        scrollbar = ttk.Scrollbar(results_frame, orient="vertical", command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        self.results_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def log(self, message):
        """إضافة رسالة للنتائج"""
        self.results_text.insert(tk.END, f"{message}\n")
        self.results_text.see(tk.END)
        self.root.update_idletasks()

    def browse_input(self):
        """تصفح مجلد الإدخال"""
        folder = filedialog.askdirectory(title="اختر مجلد البيانات الخام")
        if folder:
            self.input_folder.set(folder)
            files = list(Path(folder).glob("*.*"))
            self.status.set(f"تم اختيار المجلد - وجد {len(files)} ملف")

    def browse_output(self):
        """تصفح مجلد الإخراج"""
        folder = filedialog.askdirectory(title="اختر مجلد النتائج")
        if folder:
            self.output_folder.set(folder)

    def start_processing(self):
        """بدء المعالجة النهائية"""
        if not self.input_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإدخال")
            return
            
        if not self.output_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإخراج")
            return
        
        self.results_text.delete(1.0, tk.END)
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.processing = True
        
        Thread(target=self.processing_thread, daemon=True).start()

    def stop_processing(self):
        """إيقاف المعالجة"""
        self.processing = False
        self.status.set("جاري إيقاف العملية...")

    def processing_thread(self):
        """معالجة الملفات في thread منفصل"""
        try:
            self.log("🎯 بدء المعالجة النهائية الشاملة...")
            self.log(f"⟨⟩ الفاصل الذكي المستخدم: {self.SMART_DELIMITER}")
            
            input_path = Path(self.input_folder.get())
            files = list(input_path.glob("*.csv")) + list(input_path.glob("*.txt"))
            
            if not files:
                self.log("❌ لا توجد ملفات للمعالجة")
                return
            
            self.log(f"📊 عدد الملفات: {len(files)}")
            
            # معالجة جميع الملفات
            all_data = []
            
            for i, file_path in enumerate(files):
                if not self.processing:
                    break
                
                self.log(f"\n📄 معالجة الملف {i+1}/{len(files)}: {file_path.name}")
                self.status.set(f"معالجة {file_path.name}")
                self.progress.set((i / len(files)) * 100)
                
                file_data = self.process_single_file(file_path)
                if file_data:
                    all_data.extend(file_data)
                
                self.root.update_idletasks()
            
            if all_data and self.processing:
                self.log(f"\n💾 حفظ النتائج النهائية...")
                self.save_final_results(all_data)
                
                self.log(f"\n🎉 تم الانتهاء بنجاح!")
                self.log(f"📊 إجمالي السجلات: {len(all_data)}")
                self.status.set("✅ تم الانتهاء!")
                messagebox.showinfo("مكتمل", f"تم معالجة {len(all_data)} سجل بنجاح!")
            
        except Exception as e:
            self.log(f"❌ خطأ عام: {str(e)}")
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")
        finally:
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')

    def process_single_file(self, file_path):
        """معالجة ملف واحد"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            lines = [line.strip() for line in content.split('\n') if line.strip()]
            self.log(f"📋 قراءة {len(lines)} سطر")
            
            processed_data = []
            
            for line in lines:
                if self.fix_merged_data.get():
                    # إصلاح البيانات الملتصقة
                    separated_data = self.separate_merged_data(line)
                else:
                    # معالجة عادية
                    separated_data = self.process_normal_line(line)
                
                if separated_data and any(cell.strip() for cell in separated_data):
                    processed_data.append(separated_data)
            
            self.log(f"✅ تم معالجة {len(processed_data)} سجل")
            return processed_data
            
        except Exception as e:
            self.log(f"❌ خطأ في معالجة {file_path.name}: {str(e)}")
            return []

    def separate_merged_data(self, line):
        """فصل البيانات الملتصقة باستخدام الأنماط"""
        separated = [''] * len(self.FINAL_HEADERS)
        
        try:
            # استخراج Facebook ID
            fb_match = re.search(self.DATA_PATTERNS['facebook_id'], line)
            if fb_match:
                separated[0] = fb_match.group()
                line = line.replace(fb_match.group(), self.SMART_DELIMITER, 1)
            
            # استخراج الهاتف
            phone_match = re.search(self.DATA_PATTERNS['phone'], line)
            if phone_match:
                separated[2] = phone_match.group()
                line = line.replace(phone_match.group(), self.SMART_DELIMITER, 1)
            
            # استخراج الإيميل
            email_match = re.search(self.DATA_PATTERNS['email'], line)
            if email_match:
                separated[1] = email_match.group()
                line = line.replace(email_match.group(), self.SMART_DELIMITER, 1)
            
            # استخراج الرابط
            url_match = re.search(self.DATA_PATTERNS['url'], line)
            if url_match:
                separated[8] = url_match.group()
                line = line.replace(url_match.group(), self.SMART_DELIMITER, 1)
            
            # استخراج الموقع
            location_match = re.search(self.DATA_PATTERNS['location'], line)
            if location_match:
                location_parts = location_match.group().split()
                if len(location_parts) >= 2:
                    separated[14] = location_parts[0]  # hometown
                    separated[15] = location_parts[1]  # country
                line = line.replace(location_match.group(), self.SMART_DELIMITER, 1)
            
            # استخراج التاريخ
            date_match = re.search(self.DATA_PATTERNS['date'], line)
            if date_match:
                year = date_match.group().split('/')[-1]
                separated[4] = year
                line = line.replace(date_match.group(), self.SMART_DELIMITER, 1)
            
            # معالجة النص المتبقي
            remaining_parts = [part.strip() for part in line.split(self.SMART_DELIMITER) if part.strip()]
            
            # محاولة استخراج الاسم من النص العربي
            for part in remaining_parts:
                if re.search(r'[\u0600-\u06FF]', part):  # نص عربي
                    if not separated[10]:  # fullname
                        separated[10] = part
                        name_parts = part.split()
                        if len(name_parts) >= 2:
                            separated[5] = name_parts[0]  # first_name
                            separated[6] = name_parts[-1]  # last_name
                    break
            
        except Exception as e:
            self.log(f"❌ خطأ في فصل البيانات: {str(e)}")
        
        return separated

    def process_normal_line(self, line):
        """معالجة السطر العادي"""
        # إزالة الفواصل المتعددة
        line = re.sub(r',{2,}', ',', line)
        
        # تقسيم البيانات
        parts = [part.strip().replace('"', '') for part in line.split(',')]
        
        # توحيد مع النسق الثابت
        standardized = [''] * len(self.FINAL_HEADERS)
        for i, part in enumerate(parts):
            if i < len(standardized):
                standardized[i] = part
        
        return standardized

    def save_final_results(self, data):
        """حفظ النتائج النهائية"""
        output_format = self.output_format.get()
        
        # إضافة الرؤوس
        final_data = [self.FINAL_HEADERS] + data
        
        try:
            if output_format in ["csv", "all"]:
                # حفظ CSV
                csv_file = Path(self.output_folder.get()) / "FINAL_CLEANED_DATA.csv"
                with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerows(final_data)
                self.log(f"💾 تم حفظ CSV: {csv_file.name}")
            
            if output_format in ["db", "all"]:
                # حفظ قاعدة البيانات
                db_file = Path(self.output_folder.get()) / "FINAL_CLEANED_DATA.db"
                self.create_sqlite_database(final_data, db_file)
                self.log(f"🗄️ تم حفظ قاعدة البيانات: {db_file.name}")
            
        except Exception as e:
            self.log(f"❌ خطأ في الحفظ: {str(e)}")

    def create_sqlite_database(self, data, db_file):
        """إنشاء قاعدة بيانات SQLite محسنة"""
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # إنشاء الجدول مع فهارس
            headers = data[0]
            create_table_sql = f"""
            CREATE TABLE IF NOT EXISTS cleaned_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                {', '.join([f'{header} TEXT' for header in headers])}
            )
            """
            cursor.execute(create_table_sql)
            
            # إدراج البيانات
            placeholders = ', '.join(['?' for _ in headers])
            insert_sql = f"INSERT INTO cleaned_data ({', '.join(headers)}) VALUES ({placeholders})"
            
            cursor.executemany(insert_sql, data[1:])
            
            # إنشاء فهارس للبحث السريع
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_facebook_id ON cleaned_data(facebook_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_email ON cleaned_data(email)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_phone ON cleaned_data(phone)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_fullname ON cleaned_data(fullname)")
            
            conn.commit()
            conn.close()
            
            self.log(f"🗄️ تم إنشاء قاعدة البيانات مع {len(data)-1} سجل")
            
        except Exception as e:
            self.log(f"❌ خطأ في إنشاء قاعدة البيانات: {str(e)}")

    def open_results(self):
        """فتح مجلد النتائج"""
        if self.output_folder.get() and os.path.exists(self.output_folder.get()):
            os.startfile(self.output_folder.get())


def main():
    """تشغيل الأداة النهائية"""
    root = tk.Tk()
    app = UltimateDataCleaner(root)
    root.mainloop()


if __name__ == "__main__":
    main()

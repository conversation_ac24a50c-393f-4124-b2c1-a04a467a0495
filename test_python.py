#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("Python is working!")
print("Testing imports...")

try:
    import tkinter as tk
    print("✓ tkinter imported successfully")
except ImportError as e:
    print(f"✗ tkinter import failed: {e}")

try:
    import csv
    print("✓ csv imported successfully")
except ImportError as e:
    print(f"✗ csv import failed: {e}")

try:
    import re
    print("✓ re imported successfully")
except ImportError as e:
    print(f"✗ re import failed: {e}")

try:
    import pathlib
    print("✓ pathlib imported successfully")
except ImportError as e:
    print(f"✗ pathlib import failed: {e}")

print("\nAll tests completed!")
input("Press Enter to exit...")

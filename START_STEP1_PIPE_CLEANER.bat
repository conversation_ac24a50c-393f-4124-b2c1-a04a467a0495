@echo off
title STEP 1 - Pipe Data Cleaner

echo ========================================
echo    STEP 1 - PIPE DATA CLEANER
echo ========================================
echo.
echo Starting Data Cleaning with Pipe Delimiter...
echo.
echo Features:
echo - Replaces "," with "|"
echo - <PERSON><PERSON> multiple commas (,,, → |)
echo - Handles merged data intelligently
echo - Outputs clean files ready for database
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    echo.
    pause
    exit /b 1
)

REM Check if the Python file exists
if not exist "STEP1_PIPE_CLEANER.py" (
    echo ERROR: STEP1_PIPE_CLEANER.py not found
    echo Please make sure you are in the correct directory
    echo Current directory: %CD%
    echo.
    pause
    exit /b 1
)

echo Starting Python script...
echo.

python STEP1_PIPE_CLEANER.py

if errorlevel 1 (
    echo.
    echo ERROR: Failed to start Pipe Data Cleaner
    echo Please check the error messages above
    echo.
    pause
) else (
    echo.
    echo Script completed successfully!
    echo.
    pause
)

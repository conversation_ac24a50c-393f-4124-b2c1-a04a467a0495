# 🧹 CSV Cleaner Tool - أداة تنظيف ملفات CSV

## 📋 الوصف
أداة متخصصة لتنظيف وتحسين ملفات CSV الكبيرة، خاصة البيانات الاجتماعية. تحل مشكلة البيانات غير المنظمة وتوحد تنسيق جميع الملفات.

## 🚀 التشغيل
**دبل كليك على**: `🧹 تشغيل أداة تنظيف CSV.bat`

## ✨ المميزات

### 🔧 تنظيف البيانات
- إزالة المسافات الزائدة
- تنظيف الرموز الغريبة
- إصلاح مشاكل الترميز
- تنظيف علامات الاقتباس

### 📊 توحيد التنسيق
- رؤوس أعمدة موحدة
- تحويل أسماء الحقول للتنسيق القياسي
- ترتيب البيانات

### 🗑️ إزالة المكررات
- كشف السجلات المتطابقة
- الاحتفاظ بنسخة واحدة فقط

### ✂️ تقسيم الملفات الكبيرة
- تقسيم الملفات أكبر من مليون سجل
- تسمية تلقائية للأجزاء

## 🎯 النسق الثابت للبيانات الاجتماعية

الأداة تحول جميع ملفات CSV إلى النسق الثابت التالي (17 عمود):

```
0.  facebook_id      - معرف Facebook
1.  first_name       - الاسم الأول
2.  last_name        - اسم العائلة
3.  email           - البريد الإلكتروني
4.  phone           - رقم الهاتف
5.  birthday        - تاريخ الميلاد
6.  birthday_year   - سنة الميلاد
7.  locale          - اللغة/المنطقة
8.  hometown        - المدينة الأصلية
9.  work            - العمل
10. country         - البلد
11. education       - التعليم
12. relationship    - الحالة الاجتماعية
13. religion        - الديانة
14. about_me        - نبذة شخصية
15. gender          - الجنس
16. location        - الموقع الحالي
```

### 🔄 ما تفعله الأداة:
- **تحويل أي نسق** إلى النسق الثابت أعلاه
- **ترتيب الأعمدة** حسب الفهارس المحددة
- **إضافة أعمدة مفقودة** بقيم فارغة
- **حذف أعمدة زائدة** غير مطلوبة

## 📁 كيفية الاستخدام

### 1️⃣ تشغيل الأداة
دبل كليك على `🧹 تشغيل أداة تنظيف CSV.bat`

### 2️⃣ اختيار المجلدات
- **مجلد الإدخال**: المجلد الذي يحتوي على ملفات CSV
- **مجلد الإخراج**: مجلد حفظ الملفات المنظفة

### 3️⃣ تحديد الخيارات
- ✅ توحيد رؤوس الأعمدة (موصى به)
- ✅ تنظيف البيانات (موصى به)
- ✅ إصلاح الترميز (موصى به)
- ✅ إزالة المكررات (موصى به)
- ✅ تقسيم الملفات الكبيرة (للملفات أكبر من 1GB)

### 4️⃣ بدء التنظيف
اضغط "🚀 بدء التنظيف" وانتظر اكتمال العملية

## 🔄 مثال على التحويل

### قبل التنظيف (ملف فوضوي):
```csv
ID,Name,Mail,Tel,Sex,Location,Age,Status,Work,School,Extra1,Extra2
"100001234567890","أحمد محمد علي","<EMAIL>","01012345678","ذكر","القاهرة","28","متزوج","مهندس","جامعة القاهرة","",""
"100001234567890","أحمد محمد علي","<EMAIL>","01012345678","ذكر","القاهرة","28","متزوج","مهندس","جامعة القاهرة","",""
```

### بعد التنظيف (النسق الثابت):
```csv
facebook_id,first_name,last_name,email,phone,birthday,birthday_year,locale,hometown,work,country,education,relationship,religion,about_me,gender,location
100001234567890,أحمد,محمد,<EMAIL>,01012345678,,,,,مهندس,,جامعة القاهرة,متزوج,,,ذكر,القاهرة
```

### 🎯 الفوائد:
- **17 عمود ثابت** لجميع الملفات
- **ترتيب موحد** للبيانات
- **إزالة المكررات** تلقائياً
- **أعمدة زائدة محذوفة** (Extra1, Extra2)
- **بيانات منظمة** جاهزة للبحث السريع

## 🔧 متطلبات النظام
- **Python 3.6+** (مطلوب)
- **4GB RAM** (8GB+ للملفات الكبيرة)
- **مساحة تخزين**: ضعف حجم الملفات الأصلية

## 💡 نصائح للاستخدام الأمثل

### للملفات الكبيرة (أكبر من 1GB):
1. فعل تقسيم الملفات
2. اجعل الحد 500,000 سجل لكل ملف
3. أغلق البرامج الأخرى

### لتحسين الأداء:
1. ضع الملفات على SSD
2. تأكد من مساحة التخزين الكافية
3. احتفظ بنسخة احتياطية

## 🛠️ حل المشاكل

### "Python غير مثبت"
1. حمل من [python.org](https://python.org)
2. تأكد من "Add to PATH"
3. أعد تشغيل الكمبيوتر

### "نفدت الذاكرة"
1. قلل عدد السجلات لكل ملف
2. أغلق البرامج الأخرى
3. أعد تشغيل الكمبيوتر

## 📈 النتائج المتوقعة
- ملفات CSV منظمة ونظيفة
- رؤوس أعمدة موحدة
- بيانات جاهزة للبحث السريع
- تحسن كبير في أداء البحث

---
**هذه الأداة تحضر بياناتك للبحث السريع والفعال! 🚀**

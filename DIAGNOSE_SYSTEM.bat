@echo off
title System Diagnosis

echo ========================================
echo    SYSTEM DIAGNOSIS
echo ========================================
echo.

echo Checking current directory...
echo Current directory: %CD%
echo.

echo Checking Python installation...
python --version
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
) else (
    echo Python is available
)
echo.

echo Checking required files...
if exist "STEP1_PIPE_CLEANER.py" (
    echo ✓ STEP1_PIPE_CLEANER.py found
) else (
    echo ✗ STEP1_PIPE_CLEANER.py NOT found
)

if exist "STEP2_APPEND_DB_INSERTER.py" (
    echo ✓ STEP2_APPEND_DB_INSERTER.py found
) else (
    echo ✗ STEP2_APPEND_DB_INSERTER.py NOT found
)
echo.

echo Listing all Python files in current directory...
dir *.py /b
echo.

echo Listing all batch files in current directory...
dir *.bat /b
echo.

echo Testing Python import...
python -c "import tkinter; print('tkinter available')" 2>nul
if errorlevel 1 (
    echo WARNING: tkinter may not be available
) else (
    echo tkinter is available
)
echo.

echo Diagnosis complete!
echo.
pause

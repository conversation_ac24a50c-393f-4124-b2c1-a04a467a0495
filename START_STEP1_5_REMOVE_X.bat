@echo off
title STEP 1.5 - Remove X Columns

echo ========================================
echo    STEP 1.5 - REMOVE X COLUMNS
echo ========================================
echo.
echo Starting X Column Removal Process...
echo.
echo This step removes columns with header "x" and keeps only 19 columns:
echo facebook_id, email, phone, religion, birthday_year, first_name, 
echo last_name, gender, link, username, fullname, beo, company, title, 
echo hometown, country, education, user, status
echo.

python STEP1_5_REMOVE_X_COLUMNS.py

if errorlevel 1 (
    echo.
    echo Failed to start X Column Remover
    echo Please make sure Python is installed
    pause
)

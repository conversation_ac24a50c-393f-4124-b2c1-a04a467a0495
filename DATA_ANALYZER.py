#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 أداة تحليل البيانات لفهم الترتيب الصحيح
Data Analyzer Tool
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from pathlib import Path

class DataAnalyzer:
    def __init__(self, root):
        self.root = root
        self.root.title("🔍 أداة تحليل البيانات")
        self.root.geometry("900x700")
        
        self.PIPE_DELIMITER = "|"
        
        # النسق المطلوب
        self.TARGET_HEADERS = [
            'facebook_id', 'email', 'phone', 'religion', 'birthday_year',
            'first_name', 'last_name', 'gender', 'link', 'username',
            'fullname', 'beo', 'company', 'title', 'hometown', 
            'country', 'education', 'user', 'status'
        ]
        
        self.setup_ui()

    def setup_ui(self):
        # العنوان
        title_label = tk.Label(self.root, text="🔍 أداة تحليل البيانات", 
                              font=('Arial', 16, 'bold'))
        title_label.pack(pady=10)
        
        # اختيار الملف
        file_frame = ttk.Frame(self.root)
        file_frame.pack(fill='x', padx=10, pady=10)
        
        ttk.Label(file_frame, text="📄 اختر ملف البيانات:").pack(anchor='w')
        self.file_path = tk.StringVar()
        
        path_frame = ttk.Frame(file_frame)
        path_frame.pack(fill='x', pady=5)
        ttk.Entry(path_frame, textvariable=self.file_path).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(path_frame, text="تصفح", command=self.browse_file).pack(side='right')
        
        # أزرار التحليل
        buttons_frame = ttk.Frame(self.root)
        buttons_frame.pack(pady=10)
        
        ttk.Button(buttons_frame, text="🔍 تحليل البيانات", 
                  command=self.analyze_data).pack(side='left', padx=5)
        ttk.Button(buttons_frame, text="📊 تحليل مفصل", 
                  command=self.detailed_analysis).pack(side='left', padx=5)
        
        # منطقة النتائج
        results_frame = ttk.LabelFrame(self.root, text="📊 نتائج التحليل", padding=10)
        results_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.results_text = tk.Text(results_frame, wrap=tk.WORD, font=('Consolas', 9))
        scrollbar = ttk.Scrollbar(results_frame, orient="vertical", command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        self.results_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def log(self, message):
        self.results_text.insert(tk.END, f"{message}\n")
        self.results_text.see(tk.END)
        self.root.update_idletasks()

    def browse_file(self):
        file_path = filedialog.askopenfilename(
            title="اختر ملف البيانات",
            filetypes=[("Text files", "*.txt"), ("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if file_path:
            self.file_path.set(file_path)

    def analyze_data(self):
        if not self.file_path.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار ملف أولاً")
            return
        
        self.results_text.delete(1.0, tk.END)
        self.log("🔍 بدء تحليل البيانات...")
        
        try:
            with open(self.file_path.get(), 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            self.log(f"📊 عدد الأسطر: {len(lines)}")
            
            if lines:
                # تحليل الرؤوس
                header_line = lines[0].strip()
                headers = header_line.split(self.PIPE_DELIMITER)
                
                self.log(f"\n📋 تحليل الرؤوس:")
                self.log(f"عدد الأعمدة: {len(headers)}")
                
                for i, header in enumerate(headers):
                    self.log(f"العمود {i}: {header}")
                
                # تحليل عينة من البيانات
                if len(lines) > 1:
                    self.log(f"\n📊 تحليل عينة من البيانات:")
                    
                    for i in range(1, min(4, len(lines))):
                        data_line = lines[i].strip()
                        if data_line:
                            data_parts = data_line.split(self.PIPE_DELIMITER)
                            self.log(f"\nالسطر {i}:")
                            self.log(f"عدد الحقول: {len(data_parts)}")
                            
                            for j, part in enumerate(data_parts):
                                if part.strip():  # عرض الحقول غير الفارغة فقط
                                    self.log(f"  الحقل {j}: {part}")
                
        except Exception as e:
            self.log(f"❌ خطأ في التحليل: {str(e)}")

    def detailed_analysis(self):
        if not self.file_path.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار ملف أولاً")
            return
        
        self.results_text.delete(1.0, tk.END)
        self.log("📊 بدء التحليل المفصل...")
        
        try:
            with open(self.file_path.get(), 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            if len(lines) < 2:
                self.log("❌ الملف لا يحتوي على بيانات كافية")
                return
            
            # تحليل الرؤوس
            header_line = lines[0].strip()
            headers = header_line.split(self.PIPE_DELIMITER)
            
            # تحليل البيانات
            sample_line = lines[1].strip()
            sample_data = sample_line.split(self.PIPE_DELIMITER)
            
            self.log(f"📋 التحليل المفصل:")
            self.log(f"عدد الأعمدة في الرؤوس: {len(headers)}")
            self.log(f"عدد الحقول في البيانات: {len(sample_data)}")
            
            self.log(f"\n🔍 تحليل كل حقل:")
            
            for i in range(max(len(headers), len(sample_data))):
                header = headers[i] if i < len(headers) else "غير محدد"
                data = sample_data[i] if i < len(sample_data) else "فارغ"
                
                # تحديد نوع البيانات
                data_type = self.identify_data_type(data)
                
                self.log(f"المؤشر {i:2d}: {header:15s} = {data:30s} ({data_type})")
            
            # اقتراح التطابق مع النسق المطلوب
            self.log(f"\n🎯 اقتراح التطابق مع النسق المطلوب:")
            
            mapping = self.suggest_mapping(headers, sample_data)
            
            for target_field, (source_index, confidence) in mapping.items():
                if source_index is not None:
                    source_header = headers[source_index] if source_index < len(headers) else "غير محدد"
                    sample_value = sample_data[source_index] if source_index < len(sample_data) else "فارغ"
                    self.log(f"{target_field:15s} ← المؤشر {source_index:2d} ({source_header}) = {sample_value} (ثقة: {confidence}%)")
                else:
                    self.log(f"{target_field:15s} ← غير موجود")
            
            # إنشاء كود التطابق
            self.log(f"\n💻 كود التطابق المقترح:")
            self.generate_mapping_code(mapping)
            
        except Exception as e:
            self.log(f"❌ خطأ في التحليل المفصل: {str(e)}")

    def identify_data_type(self, data):
        """تحديد نوع البيانات"""
        if not data or data.strip() == '':
            return "فارغ"
        
        data = data.strip()
        
        # فحص الأرقام الطويلة (Facebook ID)
        if data.isdigit() and len(data) > 10:
            return "Facebook ID"
        
        # فحص الهاتف
        if data.startswith('+') and data[1:].isdigit():
            return "رقم هاتف"
        
        # فحص الإيميل
        if '@' in data and '.' in data:
            return "إيميل"
        
        # فحص الرابط
        if data.startswith('http'):
            return "رابط"
        
        # فحص النص العربي
        if any('\u0600' <= char <= '\u06FF' for char in data):
            return "نص عربي"
        
        # فحص الجنس
        if data.lower() in ['male', 'female', 'ذكر', 'أنثى']:
            return "جنس"
        
        return "نص"

    def suggest_mapping(self, headers, sample_data):
        """اقتراح تطابق الحقول"""
        mapping = {}
        
        for target_field in self.TARGET_HEADERS:
            best_match = None
            best_confidence = 0
            
            for i, (header, data) in enumerate(zip(headers, sample_data)):
                confidence = self.calculate_confidence(target_field, header, data)
                if confidence > best_confidence:
                    best_confidence = confidence
                    best_match = i
            
            mapping[target_field] = (best_match if best_confidence > 30 else None, best_confidence)
        
        return mapping

    def calculate_confidence(self, target_field, header, data):
        """حساب مستوى الثقة في التطابق"""
        confidence = 0
        
        # تطابق اسم الحقل
        if target_field.lower() in header.lower():
            confidence += 50
        
        # تطابق نوع البيانات
        data_type = self.identify_data_type(data)
        
        if target_field == 'facebook_id' and data_type == 'Facebook ID':
            confidence += 40
        elif target_field == 'email' and data_type == 'إيميل':
            confidence += 40
        elif target_field == 'phone' and data_type == 'رقم هاتف':
            confidence += 40
        elif target_field == 'link' and data_type == 'رابط':
            confidence += 40
        elif target_field == 'gender' and data_type == 'جنس':
            confidence += 40
        elif target_field in ['first_name', 'last_name', 'fullname'] and data_type == 'نص عربي':
            confidence += 30
        
        return min(confidence, 100)

    def generate_mapping_code(self, mapping):
        """إنشاء كود التطابق"""
        self.log("# كود التطابق المقترح:")
        self.log("standardized = [''] * 19")
        
        for i, target_field in enumerate(self.TARGET_HEADERS):
            source_index, confidence = mapping[target_field]
            if source_index is not None and confidence > 50:
                self.log(f"standardized[{i:2d}] = parts[{source_index:2d}] if len(parts) > {source_index:2d} else ''  # {target_field}")
            else:
                self.log(f"standardized[{i:2d}] = ''  # {target_field} (غير موجود)")


def main():
    root = tk.Tk()
    app = DataAnalyzer(root)
    root.mainloop()


if __name__ == "__main__":
    main()

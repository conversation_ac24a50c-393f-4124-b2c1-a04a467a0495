@echo off
title CSV Cleaner - Arabic Fixed Version

echo ========================================
echo    CSV Cleaner - Arabic Support
echo ========================================
echo.
echo Starting CSV Cleaner with Arabic support...
echo This version fixes Arabic text encoding issues
echo.
echo Features:
echo - Proper Arabic text display
echo - UTF-8 with BOM for Excel compatibility
echo - UTF-8 standard for general use
echo - Fixed column order (25 columns)
echo - Progress bar that works
echo.

python csv_cleaner_simple.py

if errorlevel 1 (
    echo.
    echo Failed to start CSV Cleaner
    echo Please make sure Python is installed
    pause
)

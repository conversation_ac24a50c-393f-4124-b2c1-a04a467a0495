#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧹 CSV Cleaner Tool - نسخة تشخيص
أداة تنظيف ملفات CSV/TXT مع تشخيص مفصل لحل مشاكل ملفات TXT
"""

import os
import csv
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from threading import Thread
import re
from pathlib import Path

# زيادة الحد الأقصى لحجم الحقل في CSV
maxInt = sys.maxsize
while True:
    try:
        csv.field_size_limit(maxInt)
        break
    except OverflowError:
        maxInt = int(maxInt/10)

class DebugCSVCleaner:
    def __init__(self, root):
        """تهيئة التطبيق"""
        self.root = root
        self.root.title("🧹 CSV Cleaner Tool - Debug Version")
        self.root.geometry("900x700")
        self.root.configure(pady=10, padx=10)
        
        # النسق الثابت بدون أعمدة blank (19 عمود)
        self.standard_headers = [
            'facebook_id', 'email', 'phone', 'religion', 'birthday_year',
            'first_name', 'last_name', 'gender', 'link', 'username',
            'fullname', 'beo', 'company', 'title', 'hometown', 
            'country', 'education', 'user', 'status'
        ]
        
        # المتغيرات
        self.input_folder = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.progress = tk.DoubleVar()
        self.status = tk.StringVar(value="جاهز للبدء...")
        self.current_file = tk.StringVar()
        self.processing = False
        
        # خيارات التنظيف
        self.remove_duplicates = tk.BooleanVar(value=True)
        self.standardize_headers = tk.BooleanVar(value=True)
        self.clean_data = tk.BooleanVar(value=True)
        
        # خيارات نوع الملف
        self.file_type = tk.StringVar(value="both")  # both, csv, txt
        
        # خيارات تصدير الملف المستخرج
        self.output_format = tk.StringVar(value="csv")  # csv, txt, both
        
        # إعداد الواجهة
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_label = tk.Label(
            self.root,
            text="🧹 CSV Cleaner Tool - Debug Version",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50'
        )
        title_label.pack(pady=(0, 20))
        
        # قسم المجلدات
        self.create_folder_section()
        
        # قسم الخيارات
        self.create_options_section()
        
        # قسم التحكم
        self.create_control_section()
        
        # قسم التشخيص
        self.create_debug_section()

    def create_folder_section(self):
        """إنشاء قسم اختيار المجلدات"""
        folder_frame = ttk.LabelFrame(self.root, text="📁 اختيار المجلدات", padding=10)
        folder_frame.pack(fill='x', pady=(0, 10))
        
        # مجلد الإدخال
        ttk.Label(folder_frame, text="📂 مجلد ملفات CSV/TXT:").pack(anchor='w')
        input_frame = ttk.Frame(folder_frame)
        input_frame.pack(fill='x', pady=5)
        ttk.Entry(input_frame, textvariable=self.input_folder).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(input_frame, text="تصفح", command=self.browse_input).pack(side='right')
        
        # مجلد الإخراج
        ttk.Label(folder_frame, text="💾 مجلد النتائج:").pack(anchor='w', pady=(10, 0))
        output_frame = ttk.Frame(folder_frame)
        output_frame.pack(fill='x', pady=5)
        ttk.Entry(output_frame, textvariable=self.output_folder).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(output_frame, text="تصفح", command=self.browse_output).pack(side='right')

    def create_options_section(self):
        """إنشاء قسم الخيارات"""
        options_frame = ttk.LabelFrame(self.root, text="⚙️ خيارات التنظيف", padding=10)
        options_frame.pack(fill='x', pady=(0, 10))
        
        # العمود الأيسر
        left_column = ttk.Frame(options_frame)
        left_column.pack(side='left', fill='both', expand=True)
        
        ttk.Checkbutton(left_column, text="🔧 توحيد رؤوس الأعمدة (19 عمود بدون blank)", 
                       variable=self.standardize_headers).pack(anchor='w', pady=2)
        ttk.Checkbutton(left_column, text="🧹 تنظيف البيانات", 
                       variable=self.clean_data).pack(anchor='w', pady=2)
        ttk.Checkbutton(left_column, text="🗑️ إزالة المكررات", 
                       variable=self.remove_duplicates).pack(anchor='w', pady=2)
        
        # العمود الأيمن
        right_column = ttk.Frame(options_frame)
        right_column.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        # قسم اختيار نوع الملف
        file_type_frame = ttk.LabelFrame(right_column, text="📄 نوع الملفات", padding=5)
        file_type_frame.pack(fill='x', pady=(0, 5))
        
        ttk.Radiobutton(file_type_frame, text="📊 CSV و TXT معاً", 
                       variable=self.file_type, value="both").pack(anchor='w')
        ttk.Radiobutton(file_type_frame, text="📈 ملفات CSV فقط", 
                       variable=self.file_type, value="csv").pack(anchor='w')
        ttk.Radiobutton(file_type_frame, text="📝 ملفات TXT فقط", 
                       variable=self.file_type, value="txt").pack(anchor='w')
        
        # قسم اختيار تنسيق الإخراج
        output_format_frame = ttk.LabelFrame(right_column, text="💾 تنسيق الإخراج", padding=5)
        output_format_frame.pack(fill='x', pady=(5, 0))
        
        ttk.Radiobutton(output_format_frame, text="📈 CSV فقط", 
                       variable=self.output_format, value="csv").pack(anchor='w')
        ttk.Radiobutton(output_format_frame, text="📝 TXT فقط", 
                       variable=self.output_format, value="txt").pack(anchor='w')
        ttk.Radiobutton(output_format_frame, text="📊 CSV و TXT معاً", 
                       variable=self.output_format, value="both").pack(anchor='w')

    def create_control_section(self):
        """إنشاء قسم التحكم"""
        control_frame = ttk.LabelFrame(self.root, text="🎮 التحكم والتقدم", padding=10)
        control_frame.pack(fill='x', pady=(0, 10))
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack(fill='x', pady=(0, 10))
        
        self.start_button = ttk.Button(buttons_frame, text="🚀 بدء التنظيف", 
                                      command=self.start_cleaning)
        self.start_button.pack(side='left', padx=(0, 10))
        
        self.stop_button = ttk.Button(buttons_frame, text="⏹️ إيقاف", 
                                     command=self.stop_cleaning, state='disabled')
        self.stop_button.pack(side='left', padx=(0, 10))
        
        ttk.Button(buttons_frame, text="📁 فتح النتائج", 
                  command=self.open_results).pack(side='left')
        
        # شريط التقدم
        ttk.Label(control_frame, text="📊 التقدم:").pack(anchor='w')
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress, 
                                           maximum=100, mode='determinate')
        self.progress_bar.pack(fill='x', pady=5)
        
        # معلومات الحالة
        ttk.Label(control_frame, text="📄 الملف الحالي:").pack(anchor='w', pady=(10, 0))
        ttk.Label(control_frame, textvariable=self.current_file, 
                 font=('Arial', 9), foreground='blue').pack(anchor='w')
        
        ttk.Label(control_frame, text="ℹ️ الحالة:").pack(anchor='w', pady=(5, 0))
        ttk.Label(control_frame, textvariable=self.status, 
                 font=('Arial', 9), foreground='navy').pack(anchor='w')

    def create_debug_section(self):
        """إنشاء قسم التشخيص"""
        debug_frame = ttk.LabelFrame(self.root, text="🔍 تشخيص العملية", padding=10)
        debug_frame.pack(fill='both', expand=True)
        
        # منطقة النص للتشخيص
        self.debug_text = tk.Text(debug_frame, height=8, wrap=tk.WORD, 
                                 font=('Consolas', 9))
        scrollbar = ttk.Scrollbar(debug_frame, orient="vertical", command=self.debug_text.yview)
        self.debug_text.configure(yscrollcommand=scrollbar.set)
        
        self.debug_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def log_debug(self, message):
        """إضافة رسالة تشخيص"""
        self.debug_text.insert(tk.END, f"{message}\n")
        self.debug_text.see(tk.END)
        self.root.update_idletasks()

    def browse_input(self):
        """تصفح مجلد الإدخال"""
        folder = filedialog.askdirectory(title="اختر مجلد ملفات CSV/TXT")
        if folder:
            self.input_folder.set(folder)
            csv_files = list(Path(folder).glob("*.csv"))
            txt_files = list(Path(folder).glob("*.txt"))
            total_files = len(csv_files) + len(txt_files)
            self.status.set(f"تم اختيار المجلد - وجد {total_files} ملف")
            self.log_debug(f"📁 تم اختيار المجلد: {folder}")
            self.log_debug(f"📈 ملفات CSV: {len(csv_files)}")
            self.log_debug(f"📝 ملفات TXT: {len(txt_files)}")

    def browse_output(self):
        """تصفح مجلد الإخراج"""
        folder = filedialog.askdirectory(title="اختر مجلد النتائج")
        if folder:
            self.output_folder.set(folder)
            self.log_debug(f"💾 تم اختيار مجلد النتائج: {folder}")

    def start_cleaning(self):
        """بدء عملية التنظيف"""
        if not self.input_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإدخال")
            return
            
        if not self.output_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإخراج")
            return
        
        # مسح منطقة التشخيص
        self.debug_text.delete(1.0, tk.END)
        
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.processing = True
        
        Thread(target=self.cleaning_thread, daemon=True).start()

    def stop_cleaning(self):
        """إيقاف عملية التنظيف"""
        self.processing = False
        self.status.set("جاري إيقاف العملية...")
        self.log_debug("⏹️ تم طلب إيقاف العملية")

    def cleaning_thread(self):
        """معالجة الملفات في thread منفصل"""
        try:
            self.log_debug("🚀 بدء عملية التنظيف...")
            
            input_path = Path(self.input_folder.get())
            
            # جمع الملفات حسب النوع المختار
            files_to_process = []
            file_type = self.file_type.get()
            
            self.log_debug(f"📄 نوع الملفات المختار: {file_type}")
            
            if file_type == "both":
                files_to_process.extend(list(input_path.glob("*.csv")))
                files_to_process.extend(list(input_path.glob("*.txt")))
            elif file_type == "csv":
                files_to_process = list(input_path.glob("*.csv"))
            elif file_type == "txt":
                files_to_process = list(input_path.glob("*.txt"))
            
            self.log_debug(f"📊 عدد الملفات للمعالجة: {len(files_to_process)}")
            
            if not files_to_process:
                file_types_msg = {
                    "both": "CSV أو TXT",
                    "csv": "CSV", 
                    "txt": "TXT"
                }
                error_msg = f"لا توجد ملفات {file_types_msg[file_type]} في المجلد"
                self.log_debug(f"❌ {error_msg}")
                messagebox.showwarning("تنبيه", error_msg)
                return
            
            total_files = len(files_to_process)
            processed_files = 0
            
            for i, data_file in enumerate(files_to_process):
                if not self.processing:
                    break
                
                self.log_debug(f"\n📄 معالجة الملف {i+1}/{total_files}: {data_file.name}")
                
                # تحديث الواجهة
                self.current_file.set(data_file.name)
                self.status.set(f"معالجة الملف {i+1} من {total_files}")
                self.progress.set((i / total_files) * 100)
                self.root.update_idletasks()
                
                # معالجة الملف
                if self.process_file_debug(data_file):
                    processed_files += 1
                    self.log_debug(f"✅ تم معالجة {data_file.name} بنجاح")
                else:
                    self.log_debug(f"❌ فشل في معالجة {data_file.name}")
                
                # تحديث التقدم
                self.progress.set(((i + 1) / total_files) * 100)
                self.root.update_idletasks()
            
            if self.processing:
                success_msg = f"✅ تم الانتهاء! معالج {processed_files} من {total_files} ملف"
                self.status.set(success_msg)
                self.log_debug(f"\n🎉 {success_msg}")
                messagebox.showinfo("مكتمل", f"تم تنظيف {processed_files} ملف بنجاح!")
            else:
                self.status.set("❌ تم إيقاف العملية")
                self.log_debug("❌ تم إيقاف العملية بواسطة المستخدم")
                
        except Exception as e:
            error_msg = f"حدث خطأ: {str(e)}"
            self.log_debug(f"💥 خطأ عام: {error_msg}")
            messagebox.showerror("خطأ", error_msg)
        finally:
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')

    def process_file_debug(self, input_file):
        """معالجة ملف مع تشخيص مفصل"""
        try:
            file_extension = input_file.suffix.lower()
            self.log_debug(f"📋 نوع الملف: {file_extension}")
            
            # قراءة الملف
            data_rows = []
            
            with open(input_file, 'r', encoding='utf-8', errors='ignore') as f:
                # كشف الفاصل
                sample = f.read(1024)
                f.seek(0)
                
                comma_count = sample.count(',')
                semicolon_count = sample.count(';')
                tab_count = sample.count('\t')
                
                self.log_debug(f"🔍 فحص الفواصل - فاصلة: {comma_count}, فاصلة منقوطة: {semicolon_count}, Tab: {tab_count}")
                
                if file_extension == '.txt':
                    delimiter = '\t' if tab_count > max(comma_count, semicolon_count) else ','
                else:
                    delimiter = ',' if comma_count >= semicolon_count else ';'
                
                self.log_debug(f"🎯 الفاصل المختار: '{delimiter}'")
                
                csv_reader = csv.reader(f, delimiter=delimiter)
                data_rows = list(csv_reader)
            
            self.log_debug(f"📊 تم قراءة {len(data_rows)} سطر")
            
            if len(data_rows) <= 1:
                self.log_debug(f"⚠️ الملف لا يحتوي على بيانات كافية")
                return False
            
            # عرض عينة من البيانات
            self.log_debug(f"📋 رؤوس الأعمدة: {data_rows[0][:5]}...")  # أول 5 أعمدة
            if len(data_rows) > 1:
                self.log_debug(f"📋 السطر الأول: {data_rows[1][:5]}...")  # أول 5 خلايا
            
            # تنظيف البيانات
            if self.clean_data.get():
                self.log_debug("🧹 تنظيف البيانات...")
                data_rows = self.clean_data_simple(data_rows)
            
            # توحيد الرؤوس
            if self.standardize_headers.get():
                self.log_debug("🔧 توحيد رؤوس الأعمدة...")
                original_count = len(data_rows)
                data_rows = self.apply_standard_format_simple(data_rows)
                self.log_debug(f"📊 بعد التوحيد: {len(data_rows)} سطر, {len(data_rows[0]) if data_rows else 0} عمود")
            
            # إزالة المكررات
            if self.remove_duplicates.get():
                self.log_debug("🗑️ إزالة المكررات...")
                original_count = len(data_rows)
                data_rows = self.remove_duplicates_simple(data_rows)
                removed_count = original_count - len(data_rows)
                self.log_debug(f"🗑️ تم حذف {removed_count} مكرر")
            
            # التحقق من وجود بيانات بعد المعالجة
            if len(data_rows) <= 1:
                self.log_debug(f"⚠️ لا توجد بيانات بعد المعالجة")
                return False
            
            # حفظ الملف
            self.log_debug(f"💾 حفظ الملف بتنسيق: {self.output_format.get()}")
            saved_files = self.save_output_files(data_rows, input_file.stem)
            
            if saved_files:
                files_info = ", ".join([f.name for f in saved_files])
                self.log_debug(f"💾 تم حفظ: {files_info}")
                self.log_debug(f"📊 البيانات النهائية: {len(data_rows)-1} سجل, {len(data_rows[0])} عمود")
                return True
            else:
                self.log_debug(f"❌ فشل في حفظ الملفات")
                return False
                
        except Exception as e:
            self.log_debug(f"💥 خطأ في معالجة {input_file.name}: {str(e)}")
            return False

    def clean_data_simple(self, data_rows):
        """تنظيف البيانات - نسخة مبسطة"""
        cleaned_rows = []
        for row in data_rows:
            cleaned_row = []
            for cell in row:
                if isinstance(cell, str):
                    cell = cell.strip().replace('"', '').replace("'", '')
                    cell = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', cell)
                    cell = re.sub(r'\s+', ' ', cell).strip()
                cleaned_row.append(cell)
            cleaned_rows.append(cleaned_row)
        return cleaned_rows

    def apply_standard_format_simple(self, data_rows):
        """تطبيق النسق الثابت - نسخة مبسطة"""
        if not data_rows:
            return [self.standard_headers]
        
        standardized_data = [self.standard_headers]
        
        # إذا لم تكن هناك رؤوس، أضف البيانات مباشرة
        if len(data_rows) == 1:
            # ملف بدون رؤوس، البيانات في السطر الأول
            row = data_rows[0]
            new_row = [''] * len(self.standard_headers)
            for i, cell in enumerate(row):
                if i < len(new_row):
                    new_row[i] = str(cell).replace('"', '').strip()
            if any(cell.strip() for cell in new_row):
                standardized_data.append(new_row)
        else:
            # ملف مع رؤوس
            for row in data_rows[1:]:
                new_row = [''] * len(self.standard_headers)
                for i, cell in enumerate(row):
                    if i < len(new_row):
                        new_row[i] = str(cell).replace('"', '').strip()
                if any(cell.strip() for cell in new_row):
                    standardized_data.append(new_row)
        
        return standardized_data

    def remove_duplicates_simple(self, data_rows):
        """إزالة المكررات - نسخة مبسطة"""
        if len(data_rows) <= 1:
            return data_rows
        
        headers = data_rows[0]
        seen = set()
        unique_rows = [headers]
        
        for row in data_rows[1:]:
            row_key = tuple(str(cell).strip().lower() for cell in row)
            if row_key not in seen:
                seen.add(row_key)
                unique_rows.append(row)
        
        return unique_rows

    def save_output_files(self, data_rows, base_filename):
        """حفظ الملفات بالتنسيقات المختارة"""
        saved_files = []
        output_format = self.output_format.get()
        
        try:
            if output_format == "csv" or output_format == "both":
                csv_file = Path(self.output_folder.get()) / f"{base_filename}_cleaned.csv"
                with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f, delimiter=',', quoting=csv.QUOTE_MINIMAL)
                    writer.writerows(data_rows)
                saved_files.append(csv_file)
            
            if output_format == "txt" or output_format == "both":
                txt_file = Path(self.output_folder.get()) / f"{base_filename}_cleaned.txt"
                with open(txt_file, 'w', encoding='utf-8') as f:
                    for row in data_rows:
                        line = '\t'.join(str(cell) for cell in row)
                        f.write(line + '\n')
                saved_files.append(txt_file)
            
            return saved_files
            
        except Exception as e:
            self.log_debug(f"💥 خطأ في حفظ الملفات: {str(e)}")
            return []

    def open_results(self):
        """فتح مجلد النتائج"""
        if self.output_folder.get() and os.path.exists(self.output_folder.get()):
            os.startfile(self.output_folder.get())


def main():
    """تشغيل التطبيق"""
    root = tk.Tk()
    app = DebugCSVCleaner(root)
    root.mainloop()


if __name__ == "__main__":
    main()

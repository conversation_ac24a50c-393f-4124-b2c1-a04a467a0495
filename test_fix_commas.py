#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لإصلاح الفواصل المتعددة
"""

import csv
from pathlib import Path
from io import StringIO

def fix_multiple_commas_in_line(line):
    """إصلاح الفواصل المتعددة المتتالية في السطر مع الاحتفاظ بعلامات التنصيص"""
    print(f"قبل الإصلاح: {line[:100]}...")
    
    # الجولة الأولى: إصلاح الفواصل من 6 إلى 2
    line = line.replace(',,,,,,', ',')  # 6 فواصل
    line = line.replace(',,,,,', ',')   # 5 فواصل
    line = line.replace(',,,,', ',')    # 4 فواصل
    line = line.replace(',,,', ',')     # 3 فواصل
    line = line.replace(',,', ',')      # فاصلتان
    
    # الجولة الثانية: تكرار العملية لضمان عدم وجود فواصل زائدة
    line = line.replace(',,,,,,', ',')  # 6 فواصل
    line = line.replace(',,,,,', ',')   # 5 فواصل
    line = line.replace(',,,,', ',')    # 4 فواصل
    line = line.replace(',,,', ',')     # 3 فواصل
    line = line.replace(',,', ',')      # فاصلتان
    
    print(f"بعد الإصلاح: {line[:100]}...")
    return line

def test_fix_commas():
    """اختبار إصلاح الفواصل المتعددة"""
    
    # ملف الاختبار
    input_file = Path("test_multiple_commas.csv")
    output_file = Path("test_output_fixed.csv")
    
    if not input_file.exists():
        print(f"❌ ملف الاختبار غير موجود: {input_file}")
        return
    
    print(f"📂 قراءة الملف: {input_file}")
    
    # قراءة الملف
    with open(input_file, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    print(f"📊 حجم المحتوى الأصلي: {len(content)} حرف")
    
    # إصلاح الفواصل المتعددة
    lines = content.split('\n')
    fixed_lines = []
    
    for i, line in enumerate(lines):
        if line.strip():
            print(f"\n--- السطر {i+1} ---")
            fixed_line = fix_multiple_commas_in_line(line)
            fixed_lines.append(fixed_line)
        else:
            fixed_lines.append(line)
    
    content = '\n'.join(fixed_lines)
    
    print(f"📊 حجم المحتوى بعد الإصلاح: {len(content)} حرف")
    
    # تحويل إلى CSV reader
    csv_reader = csv.reader(StringIO(content), delimiter=',')
    data_rows = list(csv_reader)
    
    print(f"📊 عدد الأسطر: {len(data_rows)}")
    if data_rows:
        print(f"📊 عدد الأعمدة في السطر الأول: {len(data_rows[0])}")
        print(f"📊 الرؤوس: {data_rows[0][:5]}...")
        if len(data_rows) > 1:
            print(f"📊 السطر الأول: {data_rows[1][:5]}...")
    
    # حفظ النتيجة
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f, delimiter=',', quoting=csv.QUOTE_MINIMAL)
        writer.writerows(data_rows)
    
    print(f"✅ تم حفظ النتيجة في: {output_file}")
    print(f"📊 عدد الأسطر المحفوظة: {len(data_rows)}")

if __name__ == "__main__":
    test_fix_commas()

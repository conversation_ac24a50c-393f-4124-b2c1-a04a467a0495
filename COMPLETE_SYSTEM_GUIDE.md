# 🎯 النظام الكامل - من البيانات الخام إلى البحث المتقدم
## Complete System - From Raw Data to Advanced Search

## 🏗️ نظرة عامة على النظام

### **المراحل الثلاث**:
1. **🧹 المرحلة الأولى**: تنظيف البيانات (26 عمود → 19 عمود)
2. **📊 المرحلة الثانية**: إدراج في قاعدة البيانات SQLite
3. **🔍 المرحلة الثالثة**: البحث المتقدم والاستخراج

---

## 📊 النسق النهائي (19 عمود)

### **الحقول المتاحة**:
```
1.  facebook_id     - معرف الفيسبوك
2.  email          - البريد الإلكتروني  
3.  phone          - رقم الهاتف
4.  religion       - الديانة
5.  birthday_year  - سنة الميلاد
6.  first_name     - الا<PERSON><PERSON> الأول
7.  last_name      - اسم العائلة
8.  gender         - الجنس
9.  link           - رابط الملف الشخصي
10. username       - اسم المستخدم
11. fullname       - الاسم الكامل
12. beo            - بيانات إضافية
13. company        - الشركة/المهنة
14. title          - المسمى الوظيفي
15. hometown       - المدينة
16. country        - البلد
17. education      - التعليم
18. user           - معرف المستخدم
19. status         - الحالة الاجتماعية
```

---

## 🛠️ المرحلة الأولى: تنظيف البيانات

### **الملف**: `STEP1_PIPE_CLEANER.py`
### **التشغيل**: `START_STEP1_PIPE_CLEANER.bat`

#### **الوظائف**:
- ✅ **تنظيف البيانات** من الفواصل المتعددة
- ✅ **حذف الأعمدة x** (7 أعمدة)
- ✅ **توحيد النسق** إلى 19 عمود
- ✅ **معالجة الترميز** (UTF-8, Arabic)
- ✅ **تحسين الأداء** للملفات الكبيرة

#### **الإعدادات**:
- **📂 مجلد البيانات الخام**: ملفات CSV/TXT بـ 26 عمود
- **💾 مجلد البيانات النظيفة**: مجلد للنتائج
- **📄 تنسيق الإخراج**: TXT مع | (موصى به)

#### **النتيجة**:
- ملفات نظيفة بـ 19 عمود بالضبط
- جاهزة للمرحلة الثانية

---

## 📊 المرحلة الثانية: قاعدة البيانات

### **الملف**: `STEP2_APPEND_DB_INSERTER.py`
### **التشغيل**: `START_STEP2_APPEND_DB.bat`

#### **الوظائف**:
- ✅ **إنشاء قاعدة البيانات** `fulldata.db`
- ✅ **إدراج البيانات** بشكل تراكمي
- ✅ **فهرسة محسنة** للبحث السريع
- ✅ **معالجة الأخطاء** والبيانات المكررة
- ✅ **تحسين الأداء** للبيانات الضخمة

#### **الإعدادات**:
- **📂 مجلد البيانات النظيفة**: نتائج المرحلة الأولى
- **🗄️ مجلد قاعدة البيانات**: مكان `fulldata.db`

#### **النتيجة**:
- `fulldata.db` مع جدول `cleaned_data`
- 19 عمود مفهرس وجاهز للبحث

---

## 🔍 المرحلة الثالثة: البحث المتقدم

### **الملف**: `STEP3_DATABASE_SEARCH.py`
### **التشغيل**: `START_STEP3_DATABASE_SEARCH.bat`

#### **الوظائف الجديدة**:
- ✅ **البحث في قواعد البيانات SQLite**
- ✅ **دعم قواعد بيانات متعددة**
- ✅ **البحث بالحقول المحددة**
- ✅ **البحث بالكلمات المفتاحية**
- ✅ **البحث بالملفات** (ID, Phone, Email, Username)
- ✅ **اختيار حقول الإخراج**
- ✅ **تنسيقات متعددة** (CSV/TXT)

### **أقسام الواجهة**:

#### **1️⃣ Select Database**:
- **➕ Add Database**: إضافة ملف `fulldata.db`
- **➖ Remove**: حذف قاعدة بيانات
- **🗑️ Clear All**: مسح جميع القواعد
- **دعم متعدد**: يمكن البحث في عدة قواعد بيانات

#### **2️⃣ Search by Value**:
##### **تبويب "البحث بالحقول"**:
- **جميع الحقول الـ 19** متاحة للبحث
- **Keywords**: البحث في جميع الحقول
- **Limit**: تحديد عدد النتائج
- **Gender**: قائمة منسدلة (male/female/ذكر/أنثى)

##### **تبويب "Select Output Fields"**:
- **اختيار الحقول** المطلوبة في النتائج
- **✅ Select All**: تحديد جميع الحقول
- **❌ Deselect All**: إلغاء التحديد

##### **تبويب "Search by File"**:
- **Data File**: ملف يحتوي على قائمة للبحث
- **Search By**: 
  - **📱 ID**: البحث بمعرفات الفيسبوك
  - **📞 Phone**: البحث بأرقام الهواتف
  - **📧 Email**: البحث بالإيميلات
  - **🔑 Keywords**: البحث بكلمات مفتاحية
  - **👤 Username**: البحث بأسماء المستخدمين

#### **3️⃣ Output Settings**:
- **Output File**: مسار ملف النتائج
- **Format**: 
  - **CSV**: ملف CSV منظم
  - **TXT**: ملف نصي بفاصل |

---

## 🎮 خطوات الاستخدام الكاملة

### **الخطوة 1: تنظيف البيانات**
```
1. دبل كليك على: START_STEP1_PIPE_CLEANER.bat
2. اختر مجلد البيانات الخام (ملفات بـ 26 عمود)
3. اختر مجلد للبيانات النظيفة
4. اختر تنسيق TXT
5. اضغط "بدء التنظيف"
```

### **الخطوة 2: إنشاء قاعدة البيانات**
```
1. دبل كليك على: START_STEP2_APPEND_DB.bat
2. اختر مجلد البيانات النظيفة (من الخطوة 1)
3. اختر مجلد لقاعدة البيانات
4. اضغط "بدء الإدراج"
5. انتظر حتى اكتمال العملية
```

### **الخطوة 3: البحث والاستخراج**
```
1. دبل كليك على: START_STEP3_DATABASE_SEARCH.bat
2. أضف قاعدة البيانات fulldata.db
3. حدد معايير البحث:
   - البحث بالحقول المحددة
   - أو البحث بالكلمات المفتاحية  
   - أو البحث بملف البيانات
4. اختر الحقول المطلوبة في النتائج
5. حدد ملف الإخراج والتنسيق
6. اضغط "Search"
```

---

## 🔍 أمثلة على البحث

### **البحث بالحقول**:
```
- First Name: أحمد
- Hometown: القاهرة
- Gender: male
- Limit: 1000
```

### **البحث بالكلمات المفتاحية**:
```
Keywords: مهندس برمجيات
```

### **البحث بالملف**:
```
Data File: facebook_ids.txt (قائمة معرفات)
Search By: ID
```

---

## 📊 أمثلة على النتائج

### **ملف الإخراج CSV**:
```csv
Facebook ID|Email|Phone|First Name|Last Name|Gender|Hometown|Company|Status
100018551915150|<EMAIL>|+201003609177|Fares|Omar|male|القاهرة|مهندس|متزوج
100018551915151|<EMAIL>|+201012345678|Ahmed|Mohamed|male|الإسكندرية|طبيب|أعزب
```

### **ملف الإخراج TXT**:
```
Facebook ID|Email|Phone|First Name|Last Name|Gender|Hometown|Company|Status
100018551915150|<EMAIL>|+201003609177|Fares|Omar|male|القاهرة|مهندس|متزوج
100018551915151|<EMAIL>|+201012345678|Ahmed|Mohamed|male|الإسكندرية|طبيب|أعزب
```

---

## 💡 نصائح للاستخدام الأمثل

### **1️⃣ للأداء الأفضل**:
- **استخدم SSD** لقاعدة البيانات
- **أغلق البرامج الأخرى** أثناء المعالجة
- **استخدم Limit** للبحثات الكبيرة

### **2️⃣ للبحث الفعال**:
- **ابدأ بمعايير محددة** ثم وسع البحث
- **استخدم Keywords** للبحث العام
- **اختر الحقول المطلوبة** فقط لتوفير المساحة

### **3️⃣ للصيانة**:
- **احتفظ بنسخة احتياطية** من fulldata.db
- **نظف البيانات الجديدة** قبل الإدراج
- **راقب حجم قاعدة البيانات**

---

## 🎉 المميزات النهائية

### **✅ ما تم تحقيقه**:
- **نظام كامل** من البيانات الخام إلى البحث المتقدم
- **19 حقل مفيد** بدلاً من 26 مع فراغات
- **قاعدة بيانات محسنة** للبحث السريع
- **واجهة مستخدم متطورة** مع خيارات متعددة
- **دعم البحث المتقدم** بطرق متنوعة
- **تنسيقات إخراج متعددة** (CSV/TXT)
- **معالجة البيانات الضخمة** بكفاءة عالية

### **🔍 خيارات البحث المتاحة**:
1. **البحث بالحقول المحددة** - دقيق ومحدد
2. **البحث بالكلمات المفتاحية** - شامل وواسع
3. **البحث بالملفات** - مجمع ومنظم
4. **البحث المختلط** - مرن ومتقدم

### **📊 أنواع البيانات المدعومة**:
- **معرفات الفيسبوك** - للبحث المباشر
- **أرقام الهواتف** - مع دعم الأرقام المصرية
- **الإيميلات** - مع دعم النطاقات المختلفة
- **الأسماء** - باللغتين العربية والإنجليزية
- **المواقع الجغرافية** - المدن والبلدان
- **البيانات المهنية** - الشركات والمسميات
- **البيانات الشخصية** - الجنس والحالة الاجتماعية

---

**🎯 الآن لديك نظام متكامل وقوي للبحث في البيانات الاجتماعية!**

**من البيانات الخام إلى النتائج المفيدة في 3 خطوات بسيطة! 🚀**

@echo off
title STEP 1 - Data Cleaner

echo ========================================
echo    STEP 1 - DATA CLEANER
echo ========================================
echo.
echo Starting Data Cleaning Process...
echo This step cleans and organizes raw data
echo using smart delimiter: ⟨⟩
echo.
echo Output: Clean data files ready for database
echo.

python STEP1_DATA_CLEANER.py

if errorlevel 1 (
    echo.
    echo Failed to start Data Cleaner
    echo Please make sure Python is installed
    pause
)

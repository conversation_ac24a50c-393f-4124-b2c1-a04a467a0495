# 📄 دعم ملفات TXT و CSV مع حل مشاكل الترميز العربي
## TXT & CSV Support with Arabic Encoding Fix

## 🎯 الميزة الجديدة

### ✨ تم إضافة:
- ✅ **دعم ملفات TXT** بالإضافة إلى CSV
- ✅ **اختيار نوع الملفات** (TXT فقط، CSV فقط، أو كلاهما)
- ✅ **معالجة محسنة للترميز العربي** في ملفات TXT
- ✅ **كشف تلقائي للفواصل** في ملفات TXT
- ✅ **إضافة رؤوس تلقائية** للملفات بدون رؤوس

---

## 🔧 حل مشاكل الترميز العربي

### ❌ المشاكل الشائعة مع العربية:
- **رموز غريبة** بدلاً من النص العربي
- **علامات استفهام** `???` بدلاً من الأحرف
- **نصوص مشوهة** مثل `Ø§Ù„Ø¹Ø±Ø¨ÙŠØ©`
- **فقدان البيانات** عند التحويل

### ✅ الحلول المطبقة:
- **اختبار ترميزات متعددة** تلقائياً
- **كشف النص العربي** والترميز المناسب
- **معالجة خاصة لملفات TXT** العربية
- **حفظ بترميز UTF-8** موحد

---

## 📊 خيارات نوع الملفات

### 🎮 الخيارات المتاحة:

#### 1️⃣ **📊 CSV و TXT معاً** (افتراضي):
- يعالج جميع ملفات `.csv` و `.txt` في المجلد
- مناسب للمجلدات المختلطة
- أفضل خيار للاستخدام العام

#### 2️⃣ **📈 ملفات CSV فقط**:
- يعالج ملفات `.csv` فقط
- يتجاهل ملفات `.txt`
- مناسب للبيانات المنظمة مسبقاً

#### 3️⃣ **📝 ملفات TXT فقط**:
- يعالج ملفات `.txt` فقط
- معالجة خاصة للترميز العربي
- مناسب للبيانات الخام أو المصدرة من قواعد البيانات

---

## 🔄 أمثلة على المعالجة

### مثال 1: ملف TXT عربي
#### قبل المعالجة (`sample_arabic_data.txt`):
```
100001234567890	أحمد محمد علي	<EMAIL>	01012345678	ذكر	القاهرة، مصر	28	متزوج	مهندس	جامعة القاهرة	مسلم	https://facebook.com/ahmed	ahmed123	مهندس برمجيات	ahmed_user	beo1
100001234567891	فاطمة علي حسن	<EMAIL>	01098765432	أنثى	الإسكندرية، مصر	25	عازبة	طبيبة	جامعة الإسكندرية	مسلمة	https://facebook.com/fatma	fatma456	طبيبة أطفال	fatma_user	beo2
```

#### بعد المعالجة (`sample_arabic_data_cleaned.csv`):
```csv
facebook_id,email,phone,religion,birthday_year,first_name,last_name,gender,link,username,fullname,beo,company,title,hometown,country,education,user,status
100001234567890,<EMAIL>,01012345678,مسلم,28,,,ذكر,https://facebook.com/ahmed,ahmed123,أحمد محمد علي,beo1,مهندس,مهندس برمجيات,القاهرة,,جامعة القاهرة,ahmed_user,متزوج
100001234567891,<EMAIL>,01098765432,مسلمة,25,,,أنثى,https://facebook.com/fatma,fatma456,فاطمة علي حسن,beo2,طبيبة,طبيبة أطفال,الإسكندرية,,جامعة الإسكندرية,fatma_user,عازبة
```

### مثال 2: ملف CSV مع مشاكل ترميز
#### قبل المعالجة:
```csv
ID,"Ø£Ø­Ù…Ø¯ Ù…Ø­Ù…Ø¯","<EMAIL>","01012345678"
```

#### بعد المعالجة:
```csv
facebook_id,email,phone,religion,birthday_year,first_name,last_name,gender,link,username,fullname,beo,company,title,hometown,country,education,user,status
ID,<EMAIL>,01012345678,,,,,,,,,,,,,,,
```

---

## 🎮 كيفية الاستخدام

### 1️⃣ تشغيل الأداة:
```
دبل كليك على: START_CSV_CLEANER.bat
أو
python csv_cleaner.py
```

### 2️⃣ اختيار نوع الملفات:
في قسم **"📄 نوع الملفات"**:
- **📊 CSV و TXT معاً** - للمجلدات المختلطة
- **📈 ملفات CSV فقط** - للبيانات المنظمة
- **📝 ملفات TXT فقط** - للبيانات الخام العربية

### 3️⃣ إعداد المجلدات:
- **مجلد الإدخال**: ملفات TXT/CSV الأصلية
- **مجلد الإخراج**: مكان حفظ الملفات المنظفة

### 4️⃣ تحديد الخيارات:
- ✅ **توحيد رؤوس الأعمدة** (19 عمود ثابت)
- ✅ **تنظيف البيانات** (إزالة الاقتباس والرموز)
- ✅ **إصلاح الترميز** (مهم للنصوص العربية)
- ✅ **إزالة المكررات**

### 5️⃣ بدء التنظيف:
- اضغط **"🚀 بدء التنظيف"**
- راقب شريط التقدم
- انتظر رسالة الإكمال

---

## 🔧 التحسينات التقنية

### 1️⃣ كشف الترميز المحسن:
```python
# قائمة ترميزات للتجربة مع ملفات TXT العربية
encodings_to_try = [
    'utf-8',
    'utf-8-sig',      # UTF-8 with BOM
    'cp1256',         # Windows Arabic
    'iso-8859-6',     # Arabic ISO
    'windows-1256',   # Windows Arabic
    'latin1',         # Latin-1
]
```

### 2️⃣ معالجة خاصة لملفات TXT:
```python
def process_txt_file(self, input_file, encoding, delimiter):
    # كشف إذا كان الملف يحتوي على رؤوس
    # إضافة رؤوس افتراضية إذا لزم الأمر
    # معالجة خاصة للترميز العربي
```

### 3️⃣ اختيار نوع الملفات:
```python
if file_type == "both":
    files_to_process.extend(list(input_path.glob("*.csv")))
    files_to_process.extend(list(input_path.glob("*.txt")))
elif file_type == "csv":
    files_to_process = list(input_path.glob("*.csv"))
elif file_type == "txt":
    files_to_process = list(input_path.glob("*.txt"))
```

---

## 📈 مقارنة الأداء

### قبل التحسين:
- ❌ **ملفات CSV فقط**
- ❌ **مشاكل ترميز عربي**
- ❌ **فقدان البيانات**
- ❌ **رموز غريبة**

### بعد التحسين:
- ✅ **دعم TXT و CSV**
- ✅ **ترميز عربي صحيح**
- ✅ **حفظ جميع البيانات**
- ✅ **نصوص عربية واضحة**

### النتيجة:
🚀 **دعم شامل للملفات العربية!**

---

## 💡 نصائح للاستخدام الأمثل

### 1️⃣ لملفات TXT العربية:
- اختر **"📝 ملفات TXT فقط"**
- فعل **"🔤 إصلاح الترميز"**
- تأكد من أن الملفات تحتوي على بيانات منفصلة بفواصل

### 2️⃣ لملفات CSV مع مشاكل ترميز:
- اختر **"📈 ملفات CSV فقط"**
- فعل **"🔤 إصلاح الترميز"**
- فعل **"🧹 تنظيف البيانات"**

### 3️⃣ للمجلدات المختلطة:
- اختر **"📊 CSV و TXT معاً"**
- فعل جميع الخيارات
- ستتم معالجة كل نوع بالطريقة المناسبة

### 4️⃣ للحصول على أفضل النتائج:
- احتفظ بنسخة احتياطية من الملفات الأصلية
- جرب على ملف صغير أولاً
- تحقق من النتائج قبل معالجة ملفات كبيرة

---

## 🎉 النتيجة النهائية

### ما حصلت عليه:
✅ **دعم ملفات TXT و CSV** معاً  
✅ **حل مشاكل الترميز العربي** تماماً  
✅ **اختيار نوع الملفات** حسب الحاجة  
✅ **معالجة محسنة للنصوص العربية**  
✅ **كشف تلقائي للفواصل** في TXT  
✅ **إضافة رؤوس تلقائية** عند الحاجة  
✅ **19 عمود ثابت** بالترتيب الصحيح  
✅ **ملفات جاهزة للبحث السريع**  

### الخطوة التالية:
🔍 **استخدم أداة البحث** للبحث في البيانات المنظفة وستحصل على نتائج سريعة ودقيقة!

---

## 📞 للمساعدة

### إذا واجهت مشاكل مع الترميز:
1. **اختر "📝 ملفات TXT فقط"** للملفات العربية
2. **فعل "🔤 إصلاح الترميز"** دائماً
3. **جرب ملف صغير** للاختبار أولاً

### إذا كانت النتائج غير صحيحة:
1. **تحقق من نوع الفاصل** في الملف الأصلي
2. **تأكد من وجود بيانات** في الملف
3. **جرب خيارات مختلفة** لنوع الملفات

---

**الآن يمكنك معالجة ملفات TXT و CSV العربية بدون أي مشاكل في الترميز! 🎯**

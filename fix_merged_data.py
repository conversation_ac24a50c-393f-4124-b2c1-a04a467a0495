#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 إصلاح البيانات الملتصقة وفصلها إلى أعمدة
Fix Merged Data and Separate into Columns
"""

import re
import csv
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from pathlib import Path

class MergedDataFixer:
    def __init__(self, root):
        self.root = root
        self.root.title("🔧 إصلاح البيانات الملتصقة")
        self.root.geometry("800x600")
        
        # أنماط البيانات المختلفة
        self.patterns = {
            'facebook_id': r'\d{10,20}',  # أرقام طويلة
            'phone': r'\+?\d{10,15}',     # أرقام هاتف
            'email': r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',  # إيميل
            'url': r'https?://[^\s]+',    # روابط
            'date': r'\d{1,2}/\d{1,2}/\d{4}',  # تواريخ
            'location': r'[A-Z][a-z]+\s+[A-Z][a-z]+',  # مواقع مثل Cairo Egypt
        }
        
        # النسق الثابت
        self.standard_headers = [
            'facebook_id', 'email', 'phone', 'religion', 'birthday_year',
            'first_name', 'last_name', 'gender', 'link', 'username',
            'fullname', 'beo', 'company', 'title', 'hometown', 
            'country', 'education', 'user', 'status'
        ]
        
        self.setup_ui()

    def setup_ui(self):
        # العنوان
        title_label = tk.Label(self.root, text="🔧 إصلاح البيانات الملتصقة", 
                              font=('Arial', 16, 'bold'))
        title_label.pack(pady=10)
        
        # اختيار الملف
        file_frame = ttk.Frame(self.root)
        file_frame.pack(fill='x', padx=10, pady=10)
        
        ttk.Label(file_frame, text="📄 اختر الملف:").pack(anchor='w')
        self.file_path = tk.StringVar()
        
        path_frame = ttk.Frame(file_frame)
        path_frame.pack(fill='x', pady=5)
        ttk.Entry(path_frame, textvariable=self.file_path).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(path_frame, text="تصفح", command=self.browse_file).pack(side='right')
        
        # أزرار المعالجة
        buttons_frame = ttk.Frame(self.root)
        buttons_frame.pack(pady=10)
        
        ttk.Button(buttons_frame, text="🔍 تحليل البيانات", 
                  command=self.analyze_data).pack(side='left', padx=5)
        ttk.Button(buttons_frame, text="🔧 إصلاح وفصل", 
                  command=self.fix_and_separate).pack(side='left', padx=5)
        ttk.Button(buttons_frame, text="💾 حفظ النتيجة", 
                  command=self.save_result).pack(side='left', padx=5)
        
        # منطقة النتائج
        results_frame = ttk.LabelFrame(self.root, text="📊 النتائج", padding=10)
        results_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.results_text = tk.Text(results_frame, wrap=tk.WORD, font=('Consolas', 9))
        scrollbar = ttk.Scrollbar(results_frame, orient="vertical", command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        self.results_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # متغير لحفظ البيانات المعالجة
        self.processed_data = []

    def log(self, message):
        """إضافة رسالة للنتائج"""
        self.results_text.insert(tk.END, f"{message}\n")
        self.results_text.see(tk.END)
        self.root.update_idletasks()

    def browse_file(self):
        """تصفح الملف"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف البيانات",
            filetypes=[("CSV files", "*.csv"), ("Text files", "*.txt"), ("All files", "*.*")]
        )
        if file_path:
            self.file_path.set(file_path)

    def analyze_data(self):
        """تحليل البيانات الملتصقة"""
        if not self.file_path.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار ملف أولاً")
            return
        
        self.results_text.delete(1.0, tk.END)
        self.log("🔍 بدء تحليل البيانات الملتصقة...")
        
        try:
            with open(self.file_path.get(), 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            lines = content.strip().split('\n')
            self.log(f"📊 عدد الأسطر: {len(lines)}")
            
            # تحليل السطر الأول كمثال
            if lines:
                sample_line = lines[0]
                self.log(f"\n📋 مثال على السطر الأول:")
                self.log(f"الطول: {len(sample_line)} حرف")
                self.log(f"المحتوى: {sample_line[:200]}...")
                
                # البحث عن الأنماط
                self.log(f"\n🔍 الأنماط المكتشفة:")
                
                for pattern_name, pattern in self.patterns.items():
                    matches = re.findall(pattern, sample_line)
                    if matches:
                        self.log(f"✅ {pattern_name}: {matches[:3]}...")  # أول 3 نتائج
                
                # محاولة فصل البيانات
                self.log(f"\n🔧 محاولة فصل البيانات:")
                separated = self.separate_line_data(sample_line)
                
                for i, item in enumerate(separated[:10]):  # أول 10 عناصر
                    self.log(f"العمود {i+1}: {item}")
                
        except Exception as e:
            self.log(f"❌ خطأ في التحليل: {str(e)}")

    def separate_line_data(self, line):
        """فصل بيانات السطر الواحد"""
        separated_data = [''] * len(self.standard_headers)
        
        try:
            # البحث عن Facebook ID (أرقام طويلة في البداية)
            fb_id_match = re.search(r'^(\d{10,20})', line)
            if fb_id_match:
                separated_data[0] = fb_id_match.group(1)
                line = line[len(fb_id_match.group(1)):]
            
            # البحث عن رقم الهاتف
            phone_match = re.search(r'\+?\d{10,15}', line)
            if phone_match:
                separated_data[2] = phone_match.group()
                line = line.replace(phone_match.group(), '', 1)
            
            # البحث عن الإيميل
            email_match = re.search(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', line)
            if email_match:
                separated_data[1] = email_match.group()
                line = line.replace(email_match.group(), '', 1)
            
            # البحث عن الرابط
            url_match = re.search(r'https?://[^\s]+', line)
            if url_match:
                separated_data[8] = url_match.group()
                line = line.replace(url_match.group(), '', 1)
            
            # البحث عن الموقع (مثل Cairo Egypt)
            location_match = re.search(r'([A-Z][a-z]+)\s+([A-Z][a-z]+)', line)
            if location_match:
                separated_data[14] = location_match.group(1)  # hometown
                separated_data[15] = location_match.group(2)  # country
                line = line.replace(location_match.group(), '', 1)
            
            # البحث عن التواريخ
            date_matches = re.findall(r'\d{1,2}/\d{1,2}/\d{4}', line)
            if date_matches:
                # أول تاريخ قد يكون تاريخ الميلاد
                birth_year = date_matches[0].split('/')[-1]
                separated_data[4] = birth_year
            
            # تنظيف النص المتبقي من الرموز الغريبة
            remaining_text = re.sub(r'[^\w\s\u0600-\u06FF]', ' ', line)
            remaining_text = re.sub(r'\s+', ' ', remaining_text).strip()
            
            # محاولة استخراج الاسم من النص المتبقي
            if remaining_text:
                # إذا كان هناك نص عربي، قد يكون الاسم
                arabic_text = re.findall(r'[\u0600-\u06FF\s]+', remaining_text)
                if arabic_text:
                    full_name = arabic_text[0].strip()
                    if full_name:
                        separated_data[10] = full_name  # fullname
                        # محاولة فصل الاسم الأول والأخير
                        name_parts = full_name.split()
                        if len(name_parts) >= 2:
                            separated_data[5] = name_parts[0]  # first_name
                            separated_data[6] = name_parts[-1]  # last_name
            
        except Exception as e:
            self.log(f"❌ خطأ في فصل البيانات: {str(e)}")
        
        return separated_data

    def fix_and_separate(self):
        """إصلاح وفصل جميع البيانات"""
        if not self.file_path.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار ملف أولاً")
            return
        
        self.results_text.delete(1.0, tk.END)
        self.log("🔧 بدء إصلاح وفصل البيانات...")
        
        try:
            with open(self.file_path.get(), 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            lines = content.strip().split('\n')
            self.processed_data = [self.standard_headers]  # إضافة الرؤوس
            
            self.log(f"📊 معالجة {len(lines)} سطر...")
            
            for i, line in enumerate(lines):
                if line.strip():
                    self.log(f"معالجة السطر {i+1}...")
                    separated = self.separate_line_data(line.strip())
                    self.processed_data.append(separated)
            
            self.log(f"✅ تم فصل البيانات إلى {len(self.processed_data)} سطر")
            self.log(f"📊 عدد الأعمدة: {len(self.standard_headers)}")
            
            # عرض عينة من النتائج
            self.log(f"\n📋 عينة من النتائج:")
            self.log(f"الرؤوس: {', '.join(self.standard_headers[:5])}...")
            
            if len(self.processed_data) > 1:
                sample_row = self.processed_data[1]
                self.log(f"السطر الأول: {', '.join(sample_row[:5])}...")
            
        except Exception as e:
            self.log(f"❌ خطأ في المعالجة: {str(e)}")

    def save_result(self):
        """حفظ النتيجة"""
        if not self.processed_data:
            messagebox.showwarning("تنبيه", "لا توجد بيانات معالجة للحفظ")
            return
        
        output_file = filedialog.asksaveasfilename(
            title="حفظ النتيجة",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        
        if output_file:
            try:
                with open(output_file, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerows(self.processed_data)
                
                self.log(f"💾 تم حفظ النتيجة في: {output_file}")
                self.log(f"📊 تم حفظ {len(self.processed_data)-1} سجل")
                messagebox.showinfo("نجح", f"تم حفظ {len(self.processed_data)-1} سجل بنجاح!")
                
            except Exception as e:
                self.log(f"❌ خطأ في الحفظ: {str(e)}")
                messagebox.showerror("خطأ", f"فشل في الحفظ: {str(e)}")


def main():
    root = tk.Tk()
    app = MergedDataFixer(root)
    root.mainloop()


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 إصلاح سريع لترتيب الأعمدة
Quick Fix for Column Mapping
"""

import csv
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from pathlib import Path

class QuickColumnFixer:
    def __init__(self, root):
        self.root = root
        self.root.title("🔧 إصلاح سريع لترتيب الأعمدة")
        self.root.geometry("800x600")
        
        self.PIPE_DELIMITER = "|"
        
        # النسق النهائي المطلوب (19 عمود)
        self.FINAL_HEADERS = [
            'facebook_id', 'email', 'phone', 'religion', 'birthday_year',
            'first_name', 'last_name', 'gender', 'link', 'username',
            'fullname', 'beo', 'company', 'title', 'hometown', 
            'country', 'education', 'user', 'status'
        ]
        
        self.setup_ui()

    def setup_ui(self):
        # العنوان
        title_label = tk.Label(self.root, text="🔧 إصلاح سريع لترتيب الأعمدة", 
                              font=('Arial', 16, 'bold'))
        title_label.pack(pady=10)
        
        # معلومات
        info_frame = ttk.LabelFrame(self.root, text="ℹ️ معلومات", padding=10)
        info_frame.pack(fill='x', padx=10, pady=10)
        
        info_text = "هذه الأداة تصحح ترتيب الأعمدة بناءً على البيانات الفعلية المرسلة"
        ttk.Label(info_frame, text=info_text).pack(anchor='w')
        
        # اختيار الملفات
        file_frame = ttk.LabelFrame(self.root, text="📁 الملفات", padding=10)
        file_frame.pack(fill='x', padx=10, pady=10)
        
        # ملف الإدخال
        ttk.Label(file_frame, text="📄 ملف الإدخال:").pack(anchor='w')
        self.input_file = tk.StringVar()
        input_frame = ttk.Frame(file_frame)
        input_frame.pack(fill='x', pady=5)
        ttk.Entry(input_frame, textvariable=self.input_file).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(input_frame, text="تصفح", command=self.browse_input).pack(side='right')
        
        # ملف الإخراج
        ttk.Label(file_frame, text="💾 ملف الإخراج:").pack(anchor='w', pady=(10, 0))
        self.output_file = tk.StringVar()
        output_frame = ttk.Frame(file_frame)
        output_frame.pack(fill='x', pady=5)
        ttk.Entry(output_frame, textvariable=self.output_file).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(output_frame, text="تصفح", command=self.browse_output).pack(side='right')
        
        # زر الإصلاح
        ttk.Button(self.root, text="🔧 إصلاح الترتيب", 
                  command=self.fix_mapping).pack(pady=20)
        
        # منطقة النتائج
        results_frame = ttk.LabelFrame(self.root, text="📊 النتائج", padding=10)
        results_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.results_text = tk.Text(results_frame, wrap=tk.WORD, font=('Consolas', 9))
        scrollbar = ttk.Scrollbar(results_frame, orient="vertical", command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=scrollbar.set)
        
        self.results_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def log(self, message):
        self.results_text.insert(tk.END, f"{message}\n")
        self.results_text.see(tk.END)
        self.root.update_idletasks()

    def browse_input(self):
        file_path = filedialog.askopenfilename(
            title="اختر ملف الإدخال",
            filetypes=[("Text files", "*.txt"), ("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if file_path:
            self.input_file.set(file_path)

    def browse_output(self):
        file_path = filedialog.asksaveasfilename(
            title="اختر ملف الإخراج",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if file_path:
            self.output_file.set(file_path)

    def fix_mapping(self):
        if not self.input_file.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار ملف الإدخال")
            return
            
        if not self.output_file.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار ملف الإخراج")
            return
        
        self.results_text.delete(1.0, tk.END)
        self.log("🔧 بدء إصلاح ترتيب الأعمدة...")
        
        try:
            # قراءة الملف
            with open(self.input_file.get(), 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            self.log(f"📊 قراءة {len(lines)} سطر")
            
            if not lines:
                self.log("❌ الملف فارغ")
                return
            
            # تحليل البيانات
            sample_line = lines[1].strip() if len(lines) > 1 else lines[0].strip()
            parts = sample_line.split(self.PIPE_DELIMITER)
            
            self.log(f"📋 تحليل البيانات:")
            self.log(f"عدد الحقول: {len(parts)}")
            
            # عرض البيانات الموجودة
            self.log(f"\n🔍 البيانات الموجودة:")
            for i, part in enumerate(parts):
                if part.strip():
                    self.log(f"المؤشر {i:2d}: {part}")
            
            # تطبيق التطابق الصحيح بناءً على البيانات المرسلة
            fixed_data = []
            
            # إضافة الرؤوس
            fixed_data.append(self.FINAL_HEADERS)
            
            # معالجة البيانات
            for line in lines[1:] if len(lines) > 1 else lines:
                line = line.strip()
                if line:
                    parts = line.split(self.PIPE_DELIMITER)
                    fixed_row = self.map_data_correctly(parts)
                    fixed_data.append(fixed_row)
            
            # حفظ النتيجة
            self.save_fixed_data(fixed_data)
            
            self.log(f"\n✅ تم الإصلاح بنجاح!")
            self.log(f"📊 عدد السجلات: {len(fixed_data) - 1}")
            self.log(f"📊 عدد الأعمدة: {len(self.FINAL_HEADERS)}")
            
            messagebox.showinfo("مكتمل", f"تم إصلاح {len(fixed_data) - 1} سجل بنجاح!")
            
        except Exception as e:
            self.log(f"❌ خطأ: {str(e)}")
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def map_data_correctly(self, parts):
        """تطبيق التطابق الصحيح بناءً على البيانات الفعلية"""
        # بناءً على البيانات المرسلة:
        # 100018551915150||+201003609177|||Fares|Omar|male|https://www.facebook.com/fares.omar.33234|fares.omar.33234|Fares Omar|||||||<EMAIL>|
        # المؤشرات: 0:facebook_id, 2:phone, 6:first_name, 7:last_name, 8:gender, 9:link, 10:username, 11:fullname, 18:email
        
        fixed_row = [''] * len(self.FINAL_HEADERS)
        
        if len(parts) >= 19:
            fixed_row[0] = parts[0] if len(parts) > 0 else ''   # facebook_id
            fixed_row[1] = parts[18] if len(parts) > 18 else '' # email (في المؤشر 18)
            fixed_row[2] = parts[2] if len(parts) > 2 else ''   # phone
            fixed_row[3] = ''  # religion (فارغ في البيانات)
            fixed_row[4] = ''  # birthday_year (فارغ في البيانات)
            fixed_row[5] = parts[6] if len(parts) > 6 else ''   # first_name
            fixed_row[6] = parts[7] if len(parts) > 7 else ''   # last_name
            fixed_row[7] = parts[8] if len(parts) > 8 else ''   # gender
            fixed_row[8] = parts[9] if len(parts) > 9 else ''   # link
            fixed_row[9] = parts[10] if len(parts) > 10 else '' # username
            fixed_row[10] = parts[11] if len(parts) > 11 else '' # fullname
            fixed_row[11] = ''  # beo (فارغ في البيانات)
            fixed_row[12] = ''  # company (فارغ في البيانات)
            fixed_row[13] = ''  # title (فارغ في البيانات)
            fixed_row[14] = ''  # hometown (فارغ في البيانات)
            fixed_row[15] = ''  # country (فارغ في البيانات)
            fixed_row[16] = ''  # education (فارغ في البيانات)
            fixed_row[17] = ''  # user (فارغ في البيانات)
            fixed_row[18] = ''  # status (فارغ في البيانات)
        else:
            # إذا كانت البيانات أقل، نحاول التعامل معها
            for i, part in enumerate(parts):
                if i < len(fixed_row):
                    fixed_row[i] = part
        
        return fixed_row

    def save_fixed_data(self, data):
        """حفظ البيانات المصححة"""
        output_path = Path(self.output_file.get())
        
        if output_path.suffix.lower() == '.csv':
            # حفظ كـ CSV
            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f, delimiter='|')
                writer.writerows(data)
        else:
            # حفظ كـ TXT
            with open(output_path, 'w', encoding='utf-8') as f:
                for row in data:
                    f.write(self.PIPE_DELIMITER.join(row) + '\n')
        
        self.log(f"💾 تم حفظ الملف: {output_path.name}")


def main():
    root = tk.Tk()
    app = QuickColumnFixer(root)
    root.mainloop()


if __name__ == "__main__":
    main()

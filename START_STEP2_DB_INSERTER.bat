@echo off
title STEP 2 - Database Inserter

echo ========================================
echo    STEP 2 - DATABASE INSERTER
echo ========================================
echo.
echo Starting Database Insertion Process...
echo This step inserts clean data into fulldata.db
echo with optimized indexes for fast searching.
echo.
echo Input: Clean data files from Step 1
echo Output: fulldata.db (SQLite database)
echo.

python STEP2_DB_INSERTER.py

if errorlevel 1 (
    echo.
    echo Failed to start Database Inserter
    echo Please make sure Python is installed
    pause
)

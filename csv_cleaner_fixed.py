#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧹 CSV Cleaner Tool - نسخة مصححة
أداة تنظيف ملفات CSV مع حل مشاكل الملفات المكررة والفارغة
"""

import os
import csv
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from threading import Thread
import re
from pathlib import Path

# زيادة الحد الأقصى لحجم الحقل في CSV
maxInt = sys.maxsize
while True:
    try:
        csv.field_size_limit(maxInt)
        break
    except OverflowError:
        maxInt = int(maxInt/10)

class FixedCSVCleaner:
    def __init__(self, root):
        """تهيئة التطبيق"""
        self.root = root
        self.root.title("🧹 CSV Cleaner Tool - Fixed Version")
        self.root.geometry("900x600")
        self.root.configure(pady=10, padx=10)
        
        # النسق الثابت (19 عمود بدون blank)
        self.standard_headers = [
            'facebook_id', 'email', 'phone', 'religion', 'birthday_year',
            'first_name', 'last_name', 'gender', 'link', 'username',
            'fullname', 'beo', 'company', 'title', 'hometown', 
            'country', 'education', 'user', 'status'
        ]
        
        # المتغيرات
        self.input_folder = tk.StringVar()
        self.output_folder = tk.StringVar()
        self.progress = tk.DoubleVar()
        self.status = tk.StringVar(value="جاهز للبدء...")
        self.current_file = tk.StringVar()
        self.processing = False
        
        # خيارات التنظيف
        self.remove_duplicates = tk.BooleanVar(value=True)
        self.standardize_headers = tk.BooleanVar(value=True)
        self.clean_data = tk.BooleanVar(value=True)

        # خيارات تصدير الملف المستخرج
        self.output_format = tk.StringVar(value="csv")  # csv, txt, both
        
        # إعداد الواجهة
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title_label = tk.Label(
            self.root,
            text="🧹 CSV Cleaner Tool - Fixed Version",
            font=('Arial', 16, 'bold'),
            fg='#2c3e50'
        )
        title_label.pack(pady=(0, 20))
        
        # قسم المجلدات
        self.create_folder_section()
        
        # قسم الخيارات
        self.create_options_section()
        
        # قسم التحكم
        self.create_control_section()

    def create_folder_section(self):
        """إنشاء قسم اختيار المجلدات"""
        folder_frame = ttk.LabelFrame(self.root, text="📁 اختيار المجلدات", padding=10)
        folder_frame.pack(fill='x', pady=(0, 10))
        
        # مجلد الإدخال
        ttk.Label(folder_frame, text="📂 مجلد ملفات CSV:").pack(anchor='w')
        input_frame = ttk.Frame(folder_frame)
        input_frame.pack(fill='x', pady=5)
        ttk.Entry(input_frame, textvariable=self.input_folder).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(input_frame, text="تصفح", command=self.browse_input).pack(side='right')
        
        # مجلد الإخراج
        ttk.Label(folder_frame, text="💾 مجلد النتائج:").pack(anchor='w', pady=(10, 0))
        output_frame = ttk.Frame(folder_frame)
        output_frame.pack(fill='x', pady=5)
        ttk.Entry(output_frame, textvariable=self.output_folder).pack(
            side='left', fill='x', expand=True, padx=(0, 5))
        ttk.Button(output_frame, text="تصفح", command=self.browse_output).pack(side='right')

    def create_options_section(self):
        """إنشاء قسم الخيارات"""
        options_frame = ttk.LabelFrame(self.root, text="⚙️ خيارات التنظيف", padding=10)
        options_frame.pack(fill='x', pady=(0, 10))
        
        # العمود الأيسر
        left_column = ttk.Frame(options_frame)
        left_column.pack(side='left', fill='both', expand=True)

        ttk.Checkbutton(left_column, text="🔧 توحيد رؤوس الأعمدة (19 عمود ثابت)",
                       variable=self.standardize_headers).pack(anchor='w', pady=2)
        ttk.Checkbutton(left_column, text="🧹 تنظيف البيانات",
                       variable=self.clean_data).pack(anchor='w', pady=2)
        ttk.Checkbutton(left_column, text="🗑️ إزالة المكررات",
                       variable=self.remove_duplicates).pack(anchor='w', pady=2)

        # العمود الأيمن
        right_column = ttk.Frame(options_frame)
        right_column.pack(side='right', fill='both', expand=True, padx=(10, 0))

        # قسم اختيار تنسيق الإخراج
        output_format_frame = ttk.LabelFrame(right_column, text="💾 تنسيق الإخراج", padding=5)
        output_format_frame.pack(fill='x')

        ttk.Radiobutton(output_format_frame, text="📈 CSV فقط",
                       variable=self.output_format, value="csv").pack(anchor='w')
        ttk.Radiobutton(output_format_frame, text="📝 TXT فقط",
                       variable=self.output_format, value="txt").pack(anchor='w')
        ttk.Radiobutton(output_format_frame, text="📊 CSV و TXT معاً",
                       variable=self.output_format, value="both").pack(anchor='w')

    def create_control_section(self):
        """إنشاء قسم التحكم"""
        control_frame = ttk.LabelFrame(self.root, text="🎮 التحكم والتقدم", padding=10)
        control_frame.pack(fill='both', expand=True)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(control_frame)
        buttons_frame.pack(fill='x', pady=(0, 10))
        
        self.start_button = ttk.Button(buttons_frame, text="🚀 بدء التنظيف", 
                                      command=self.start_cleaning)
        self.start_button.pack(side='left', padx=(0, 10))
        
        self.stop_button = ttk.Button(buttons_frame, text="⏹️ إيقاف", 
                                     command=self.stop_cleaning, state='disabled')
        self.stop_button.pack(side='left', padx=(0, 10))
        
        ttk.Button(buttons_frame, text="📁 فتح النتائج", 
                  command=self.open_results).pack(side='left')
        
        # شريط التقدم
        ttk.Label(control_frame, text="📊 التقدم:").pack(anchor='w')
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress, 
                                           maximum=100, mode='determinate')
        self.progress_bar.pack(fill='x', pady=5)
        
        # معلومات الحالة
        ttk.Label(control_frame, text="📄 الملف الحالي:").pack(anchor='w', pady=(10, 0))
        ttk.Label(control_frame, textvariable=self.current_file, 
                 font=('Arial', 9), foreground='blue').pack(anchor='w')
        
        ttk.Label(control_frame, text="ℹ️ الحالة:").pack(anchor='w', pady=(5, 0))
        ttk.Label(control_frame, textvariable=self.status, 
                 font=('Arial', 9), foreground='navy').pack(anchor='w')

    def browse_input(self):
        """تصفح مجلد الإدخال"""
        folder = filedialog.askdirectory(title="اختر مجلد ملفات CSV")
        if folder:
            self.input_folder.set(folder)
            csv_files = list(Path(folder).glob("*.csv"))
            self.status.set(f"تم اختيار المجلد - وجد {len(csv_files)} ملف CSV")

    def browse_output(self):
        """تصفح مجلد الإخراج"""
        folder = filedialog.askdirectory(title="اختر مجلد النتائج")
        if folder:
            self.output_folder.set(folder)

    def start_cleaning(self):
        """بدء عملية التنظيف"""
        if not self.input_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإدخال")
            return
            
        if not self.output_folder.get():
            messagebox.showwarning("تنبيه", "يرجى اختيار مجلد الإخراج")
            return
        
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.processing = True
        
        Thread(target=self.cleaning_thread, daemon=True).start()

    def stop_cleaning(self):
        """إيقاف عملية التنظيف"""
        self.processing = False
        self.status.set("جاري إيقاف العملية...")

    def cleaning_thread(self):
        """معالجة الملفات في thread منفصل"""
        try:
            input_path = Path(self.input_folder.get())
            csv_files = list(input_path.glob("*.csv"))
            
            if not csv_files:
                messagebox.showwarning("تنبيه", "لا توجد ملفات CSV")
                return
            
            total_files = len(csv_files)
            processed_files = 0
            
            for i, csv_file in enumerate(csv_files):
                if not self.processing:
                    break
                
                # تحديث الواجهة
                self.current_file.set(csv_file.name)
                self.status.set(f"معالجة الملف {i+1} من {total_files}")
                self.progress.set((i / total_files) * 100)
                self.root.update_idletasks()
                
                # معالجة الملف
                if self.process_file_fixed(csv_file):
                    processed_files += 1
                
                # تحديث التقدم
                self.progress.set(((i + 1) / total_files) * 100)
                self.root.update_idletasks()
            
            if self.processing:
                self.status.set(f"✅ تم الانتهاء! معالج {processed_files} من {total_files} ملف")
                messagebox.showinfo("مكتمل", f"تم تنظيف {processed_files} ملف بنجاح!")
            else:
                self.status.set("❌ تم إيقاف العملية")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")
        finally:
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')

    def process_file_fixed(self, input_file):
        """معالجة ملف CSV واحد - نسخة مصححة"""
        try:
            print(f"بدء معالجة: {input_file.name}")
            
            # قراءة الملف
            data_rows = []
            with open(input_file, 'r', encoding='utf-8', errors='ignore') as f:
                # كشف الفاصل
                sample = f.read(1024)
                f.seek(0)
                delimiter = ',' if sample.count(',') >= sample.count(';') else ';'
                
                csv_reader = csv.reader(f, delimiter=delimiter)
                data_rows = list(csv_reader)
            
            print(f"تم قراءة {len(data_rows)} سطر من {input_file.name}")
            
            if len(data_rows) <= 1:
                print(f"تحذير: {input_file.name} لا يحتوي على بيانات")
                return False
            
            # تنظيف البيانات
            if self.clean_data.get():
                data_rows = self.clean_data_simple(data_rows)
            
            # توحيد الرؤوس
            if self.standardize_headers.get():
                data_rows = self.apply_standard_format_simple(data_rows)
            
            # إزالة المكررات
            if self.remove_duplicates.get():
                original_count = len(data_rows)
                data_rows = self.remove_duplicates_simple(data_rows)
                print(f"تم حذف {original_count - len(data_rows)} مكرر")
            
            # التحقق من وجود بيانات بعد المعالجة
            if len(data_rows) <= 1:
                print(f"تحذير: لا توجد بيانات بعد المعالجة في {input_file.name}")
                return False
            
            # حفظ الملف حسب التنسيق المختار
            saved_files = self.save_output_files(data_rows, input_file.stem)

            if saved_files:
                files_info = ", ".join([f.name for f in saved_files])
                print(f"✅ تم حفظ: {files_info} - {len(data_rows)-1} سجل")
                return True
            else:
                print(f"❌ فشل في حفظ الملفات")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في معالجة {input_file.name}: {str(e)}")
            return False

    def save_output_files(self, data_rows, base_filename):
        """حفظ الملفات بالتنسيقات المختارة"""
        saved_files = []
        output_format = self.output_format.get()

        try:
            if output_format == "csv" or output_format == "both":
                # حفظ ملف CSV
                csv_file = Path(self.output_folder.get()) / f"{base_filename}_cleaned.csv"
                with open(csv_file, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f, delimiter=',', quoting=csv.QUOTE_MINIMAL)
                    writer.writerows(data_rows)
                saved_files.append(csv_file)

            if output_format == "txt" or output_format == "both":
                # حفظ ملف TXT
                txt_file = Path(self.output_folder.get()) / f"{base_filename}_cleaned.txt"
                with open(txt_file, 'w', encoding='utf-8') as f:
                    for row in data_rows:
                        # استخدام Tab كفاصل في ملفات TXT
                        line = '\t'.join(str(cell) for cell in row)
                        f.write(line + '\n')
                saved_files.append(txt_file)

            return saved_files

        except Exception as e:
            print(f"❌ خطأ في حفظ الملفات: {str(e)}")
            return []

    def clean_data_simple(self, data_rows):
        """تنظيف البيانات - نسخة مبسطة"""
        cleaned_rows = []
        for row in data_rows:
            cleaned_row = []
            for cell in row:
                if isinstance(cell, str):
                    # إزالة علامات الاقتباس والمسافات
                    cell = cell.strip().replace('"', '').replace("'", '')
                    # تنظيف الرموز الغريبة
                    cell = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', cell)
                    cell = re.sub(r'\s+', ' ', cell).strip()
                cleaned_row.append(cell)
            cleaned_rows.append(cleaned_row)
        return cleaned_rows

    def apply_standard_format_simple(self, data_rows):
        """تطبيق النسق الثابت - نسخة مبسطة"""
        if not data_rows:
            return [self.standard_headers]
        
        # إنشاء ملف جديد بالنسق الثابت
        standardized_data = [self.standard_headers]
        
        # قاموس تحويل مبسط
        header_mapping = {
            'id': 0, 'facebook_id': 0, 'fb_id': 0,
            'email': 1, 'mail': 1,
            'phone': 2, 'tel': 2, 'telephone': 2,
            'religion': 3, 'faith': 3,
            'birthday_year': 4, 'age': 4, 'year': 4,
            'first_name': 5, 'firstname': 5,
            'last_name': 6, 'lastname': 6,
            'gender': 7, 'sex': 7,
            'link': 8, 'url': 8,
            'username': 9, 'user': 9,
            'fullname': 10, 'name': 10,
            'beo': 11,
            'company': 12, 'work': 12,
            'title': 13, 'position': 13,
            'hometown': 14, 'home_town': 14,
            'country': 15, 'nation': 15,
            'education': 16, 'school': 16,
            'user': 17,
            'status': 18, 'relationship': 18
        }
        
        # تحويل رؤوس الأعمدة
        original_headers = [str(h).lower().strip().replace('"', '') for h in data_rows[0]]
        
        # معالجة البيانات
        for row in data_rows[1:]:
            new_row = [''] * len(self.standard_headers)
            
            for i, cell in enumerate(row):
                if i < len(original_headers):
                    header = original_headers[i]
                    target_index = header_mapping.get(header, -1)
                    
                    if target_index >= 0 and target_index < len(new_row):
                        clean_cell = str(cell).replace('"', '').strip()
                        new_row[target_index] = clean_cell
            
            # إضافة الصف إذا كان يحتوي على بيانات
            if any(cell.strip() for cell in new_row):
                standardized_data.append(new_row)
        
        return standardized_data

    def remove_duplicates_simple(self, data_rows):
        """إزالة المكررات - نسخة مبسطة"""
        if len(data_rows) <= 1:
            return data_rows
        
        headers = data_rows[0]
        seen = set()
        unique_rows = [headers]
        
        for row in data_rows[1:]:
            # إنشاء مفتاح فريد للصف
            row_key = tuple(str(cell).strip().lower() for cell in row)
            if row_key not in seen:
                seen.add(row_key)
                unique_rows.append(row)
        
        return unique_rows

    def open_results(self):
        """فتح مجلد النتائج"""
        if self.output_folder.get() and os.path.exists(self.output_folder.get()):
            os.startfile(self.output_folder.get())


def main():
    """تشغيل التطبيق"""
    root = tk.Tk()
    app = FixedCSVCleaner(root)
    root.mainloop()


if __name__ == "__main__":
    main()

@echo off
title STEP 3 - Database Search Tool

echo ========================================
echo    STEP 3 - DATABASE SEARCH TOOL
echo ========================================
echo.
echo Starting Database Search Tool...
echo.
echo Features:
echo - Search in SQLite databases (fulldata.db)
echo - Support for 19 columns structure
echo - Field-specific search
echo - Keywords search
echo - File-based search (ID, Phone, Email, Username)
echo - Customizable output (CSV/TXT)
echo - Multiple database support
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    echo.
    pause
    exit /b 1
)

REM Check if the Python file exists
if not exist "STEP3_DATABASE_SEARCH.py" (
    echo ERROR: STEP3_DATABASE_SEARCH.py not found
    echo Please make sure you are in the correct directory
    echo Current directory: %CD%
    echo.
    pause
    exit /b 1
)

echo Starting Database Search Tool...
echo.

python STEP3_DATABASE_SEARCH.py

if errorlevel 1 (
    echo.
    echo ERROR: Failed to start Database Search Tool
    echo Please check the error messages above
    echo.
    pause
) else (
    echo.
    echo Database Search Tool completed successfully!
    echo.
    pause
)
